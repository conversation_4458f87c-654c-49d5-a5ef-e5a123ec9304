"use client"

import React, { useState } from "react"

import { StackedBarData } from "@/types/summary"
import { DataRecord, generatePivotData } from "@/lib/pivot"
import DrillDownModal from "@/components/advanced/DrillDownModal"
import { Card } from "@/components/CardComponents"
import { CustomSelect } from "@/components/ChartComponents"
import { useWithCommentary } from "@/components/layouts/ToggleCommentary"
import { StackedBarChart } from "@/components/summary/operations-and-hr/Headcount"
import { useSummaryFilters } from "@/contexts/summary-filters"
import { useOperationsAndHRFTE } from "@/services/summary"

const getFTEFields = (data: StackedBarData[]) => {
  if (!data || data.length === 0) return []
  const keys = Object.keys(data[0]).filter((k) => k !== "name")
  return [
    {
      id: "name",
      name: "Period",
      type: "dimension",
      dataType: "string",
      iconType: "tag",
    } as const,
    ...keys.map(
      (k) =>
        ({
          id: k,
          name: k.charAt(0).toUpperCase() + k.slice(1),
          type: "measure",
          dataType: "number",
          iconType: "hash",
          aggregation: "sum",
        }) as const
    ),
  ]
}

const getInitialFTEPivotConfig = (data: StackedBarData[]) => {
  const fields = getFTEFields(data)
  return {
    filters: [],
    rows: fields.length > 0 ? [fields[0]] : [],
    columns: [],
    values: fields.slice(1),
  }
}

const OPTIONS = ["Default", "By Staff Type"]

const FTEs = () => {
  const { filters } = useSummaryFilters()
  const {
    data: { chart_data: data, commentary },
    isLoading,
  } = useOperationsAndHRFTE(filters)
  const [viewType, setViewType] = useState<string>(OPTIONS[0])

  const [withCommentary] = useWithCommentary()

  return (
    <Card
      title="FTEs"
      flashNumberLabel="(excl. Vidaskin)"
      filters={
        <CustomSelect
          value={viewType}
          setValue={setViewType}
          options={OPTIONS}
          disabled={isLoading}
        />
      }
      icons={
        <DrillDownModal
          data={data as StackedBarData[]}
          title="FTEs"
          isLoading={isLoading}
          availableFields={getFTEFields(data as StackedBarData[])}
          initialPivotConfig={getInitialFTEPivotConfig(
            data as StackedBarData[]
          )}
          defaultChart={(config) => {
            const pivotData = generatePivotData(
              data as unknown as DataRecord[],
              config
            )

            const chartData = pivotData.map(({ Period, Total, ...row }) => {
              void Total

              if (!config.values.some((item) => item.id === "total")) {
                return {
                  ...row,
                  name: Period,
                }
              }

              const calculatedTotal = Object.values(row).reduce(
                (sum, value) =>
                  Number(sum) + (typeof value === "number" ? value : 0),
                0
              )

              return {
                ...row,
                name: Period,
                total: calculatedTotal,
              }
            }) as StackedBarData[]

            return <StackedBarChart data={chartData} height="100%" />
          }}
          precision={0}
        />
      }
      commentary={withCommentary ? commentary : ""}
      isLoading={isLoading}
    >
      <StackedBarChart data={data as StackedBarData[]} />
    </Card>
  )
}

export default FTEs
