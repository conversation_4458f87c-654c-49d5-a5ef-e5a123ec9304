"use client"

import React, { useState } from "react"
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Label<PERSON>ist,
  Legend,
  Line,
  <PERSON><PERSON>hart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
} from "recharts"

import { ChartType } from "@/types/chart"
import { StackedBarData } from "@/types/summary"
import { formatAbbreviatedCurrency } from "@/lib/number"
import { DataRecord, generatePivotData } from "@/lib/pivot"
import DrillDownModal from "@/components/advanced/DrillDownModal"
import { Card, CHART_HEIGHT } from "@/components/CardComponents"
import {
  CUSTOM_CHART_COLORS,
  WrappedTick,
  WrappedTooltip,
} from "@/components/ChartComponents"
import { useWithCommentary } from "@/components/layouts/ToggleCommentary"
import { useSummaryFilters } from "@/contexts/summary-filters"
import { useOperationsAndHRHeadcount } from "@/services/summary"

const getHeadcountFields = (data: StackedBarData[]) => {
  if (!data || data.length === 0) return []
  const keys = Object.keys(data[0]).filter((k) => k !== "name")
  return [
    {
      id: "name",
      name: "Period",
      type: "dimension",
      dataType: "string",
      iconType: "tag",
    } as const,
    ...keys.map(
      (k) =>
        ({
          id: k,
          name: k.charAt(0).toUpperCase() + k.slice(1),
          type: "measure",
          dataType: "number",
          iconType: "hash",
          aggregation: "sum",
        }) as const
    ),
  ]
}

const getInitialHeadcountPivotConfig = (data: StackedBarData[]) => {
  const fields = getHeadcountFields(data)
  return {
    filters: [],
    rows: fields.length > 0 ? [fields[0]] : [],
    columns: [],
    values: fields.slice(1),
  }
}

const TABS = ["Full time", "Part time"]
const MIN_BAR_HEIGHT = 8

const Headcount = () => {
  const { filters } = useSummaryFilters()

  const [selectedTab, setSelectedTab] = useState<string>(TABS[0])

  const {
    data: { chart_data: data, commentary },
    isLoading,
  } = useOperationsAndHRHeadcount({
    type: selectedTab.toLowerCase().replace(" ", "-"),
    filters,
  })

  const [withCommentary] = useWithCommentary()

  return (
    <Card
      title="Headcount"
      tabs={TABS}
      selectedTab={selectedTab}
      onTabChange={setSelectedTab}
      flashNumberLabel="(excl. Vidaskin)"
      icons={
        <DrillDownModal
          data={data as StackedBarData[]}
          title={`Headcount (${selectedTab})`}
          isLoading={isLoading}
          availableFields={getHeadcountFields(data)}
          initialPivotConfig={getInitialHeadcountPivotConfig(data)}
          defaultChart={(config) => {
            const pivotData = generatePivotData(
              data as unknown as DataRecord[],
              config
            )

            const chartData = pivotData.map(({ Period, Total, ...row }) => {
              void Total

              if (!config.values.some((item) => item.id === "total")) {
                return {
                  ...row,
                  name: Period,
                }
              }

              const calculatedTotal = Object.values(row).reduce(
                (sum, value) =>
                  Number(sum) + (typeof value === "number" ? value : 0),
                0
              )

              return {
                ...row,
                name: Period,
                total: calculatedTotal,
              }
            }) as StackedBarData[]

            return <StackedBarChart data={chartData} height="100%" />
          }}
          precision={0}
        />
      }
      commentary={withCommentary ? commentary : ""}
      isLoading={isLoading}
    >
      <StackedBarChart data={data as unknown as StackedBarData[]} />
    </Card>
  )
}

export default Headcount

export const StackedBarChart = ({
  data,
  chartType = "Bar",
  height = CHART_HEIGHT,
  children,
}: {
  data: StackedBarData[]
  chartType?: ChartType
  height?: string | number
  children?: React.ReactNode
}) => (
  <ResponsiveContainer width="99%" height={height}>
    {chartType === "Bar" ? (
      <BarChart data={data}>
        <XAxis
          dataKey="name"
          tick={WrappedTick}
          tickLine={false}
          interval={0}
        />

        <Tooltip
          content={<WrappedTooltip />}
          formatter={(value: number) => formatAbbreviatedCurrency(value, 0)}
        />

        <Legend
          iconType="circle"
          iconSize={8}
          verticalAlign="top"
          wrapperStyle={{ fontSize: 12, top: 0 }}
        />

        {Object.keys(data?.[0] || {})
          .filter((key) => key !== "name" && key !== "total")
          .map((key, index) => (
            <Bar
              key={key}
              dataKey={key}
              name={key}
              stackId="a"
              fill={
                index < 5
                  ? `var(--chart-${index + 1})`
                  : CUSTOM_CHART_COLORS[index - 5] || "var(--chart-1)"
              }
            >
              <LabelList
                content={({ x, y, width, height, index }) => {
                  const value = data?.[index || 0]?.[key] || 0

                  const isVisible =
                    Number(value) != 0 &&
                    Math.abs(Number(height)) > MIN_BAR_HEIGHT

                  if (!isVisible) return null

                  return (
                    <text
                      x={Number(x) + Number(width) / 2}
                      y={Number(y) + Number(height) / 2}
                      fill="white"
                      fontSize={12}
                      textAnchor="middle"
                      dominantBaseline="central"
                    >
                      {formatAbbreviatedCurrency(Number(value), 0)}
                    </text>
                  )
                }}
              />
            </Bar>
          ))}

        {children}
      </BarChart>
    ) : (
      <LineChart data={data} margin={{ top: 24, left: 24, right: 24 }}>
        <XAxis
          dataKey="name"
          tick={WrappedTick}
          tickLine={false}
          interval={0}
        />

        <Tooltip
          content={<WrappedTooltip />}
          formatter={(value: number) => formatAbbreviatedCurrency(value, 0)}
        />

        <Legend verticalAlign="top" wrapperStyle={{ fontSize: 12, top: 0 }} />

        {Object.keys(data?.[0] || {})
          .filter((key) => key !== "name" && key !== "total")
          .map((key, index) => (
            <Line
              key={key}
              type="monotone"
              dataKey={key}
              name={key}
              stroke={
                index < 5
                  ? `var(--chart-${index + 1})`
                  : CUSTOM_CHART_COLORS[index - 5] || "var(--chart-1)"
              }
              strokeWidth={2}
              dot={{ r: 3 }}
              activeDot={{ r: 5 }}
            >
              <LabelList
                dataKey={key}
                position="top"
                fill="black"
                formatter={(value: number) =>
                  formatAbbreviatedCurrency(value, 0)
                }
                fontSize={12}
              />
            </Line>
          ))}

        {children}
      </LineChart>
    )}
  </ResponsiveContainer>
)
