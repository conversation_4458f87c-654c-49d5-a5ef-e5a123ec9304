"use client"

import React, { useState } from "react"

import { CHART_TYPES, ChartType } from "@/types/chart"
import { StackedBarData } from "@/types/summary"
import { DataRecord, generatePivotData, PivotField } from "@/lib/pivot"
import DrillDownModal from "@/components/advanced/DrillDownModal"
import { Card } from "@/components/CardComponents"
import { ChartTypeSelect, CustomSelect } from "@/components/ChartComponents"
import { useWithCommentary } from "@/components/layouts/ToggleCommentary"
import { StackedBarChart } from "@/components/summary/operations-and-hr/Headcount"
import { useSummaryFilters } from "@/contexts/summary-filters"
import { useOperationsAndHRClinics } from "@/services/summary"

const getClinicsFields = (data: StackedBarData[]): PivotField[] => {
  if (!data || data.length === 0) return []
  const keys = Object.keys(data[0]).filter((k) => k !== "name")
  return [
    {
      id: "name",
      name: "Period",
      type: "dimension" as const,
      dataType: "string" as const,
      iconType: "tag" as const,
    },
    ...keys.map((k) => ({
      id: k,
      name: k.charAt(0).toUpperCase() + k.slice(1),
      type: "measure" as const,
      dataType: "number" as const,
      iconType: "hash" as const,
      aggregation: "sum" as const,
    })),
  ]
}

const getInitialClinicsPivotConfig = (data: StackedBarData[]) => {
  const fields = getClinicsFields(data)
  return {
    filters: [],
    rows: fields.length > 0 ? [fields[0]] : [],
    columns: [],
    values: fields.slice(1),
  }
}

const OPTIONS = ["Default", "By Segment"]

const Clinics = () => {
  const { filters } = useSummaryFilters()
  const {
    data: { chart_data: data, commentary },
    isLoading,
  } = useOperationsAndHRClinics(filters)
  const [chartType, setChartType] = useState<ChartType>(CHART_TYPES[0])
  const [viewType, setViewType] = useState<string>(OPTIONS[0])

  const [withCommentary] = useWithCommentary()

  return (
    <Card
      title="Clinics"
      flashNumberLabel="(excl. Vidaskin)"
      filters={
        <>
          <CustomSelect
            value={viewType}
            setValue={setViewType}
            options={OPTIONS}
            disabled={isLoading}
          />

          <ChartTypeSelect
            value={chartType}
            setValue={setChartType}
            disabled={isLoading}
          />
        </>
      }
      icons={
        <DrillDownModal
          data={data as StackedBarData[]}
          title="Clinics"
          isLoading={isLoading}
          availableFields={getClinicsFields(data)}
          initialPivotConfig={getInitialClinicsPivotConfig(data)}
          defaultChart={(config) => {
            const pivotData = generatePivotData(
              data as unknown as DataRecord[],
              config
            )

            const chartData = pivotData.map(({ Period, Total, ...row }) => {
              void Total

              if (!config.values.some((item) => item.id === "total")) {
                return {
                  ...row,
                  name: Period,
                }
              }

              const calculatedTotal = Object.values(row).reduce(
                (sum, value) =>
                  Number(sum) + (typeof value === "number" ? value : 0),
                0
              )

              return {
                ...row,
                name: Period,
                total: calculatedTotal,
              }
            }) as StackedBarData[]

            return (
              <StackedBarChart
                data={chartData}
                chartType={chartType}
                height="100%"
              />
            )
          }}
          precision={0}
        />
      }
      commentary={withCommentary ? commentary : ""}
      isLoading={isLoading}
    >
      <StackedBarChart data={data as StackedBarData[]} chartType={chartType} />
    </Card>
  )
}

export default Clinics
