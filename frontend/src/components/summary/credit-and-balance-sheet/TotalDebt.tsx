"use client"

import React, { useState } from "react"

import { CHART_TYPES, ChartType } from "@/types/chart"
import { ProfitAndLossRevenueData } from "@/types/summary"
import {
  DataRecord,
  generatePivotData,
  PivotConfiguration,
  PivotField,
} from "@/lib/pivot"
import DrillDownModal from "@/components/advanced/DrillDownModal"
import { Card } from "@/components/CardComponents"
import { ChartTypeSelect } from "@/components/ChartComponents"
import { useWithCommentary } from "@/components/layouts/ToggleCommentary"
import { RevenueChart } from "@/components/summary/profit-and-loss/Revenue"
import { useCreditAndBalanceSheetTotalDebt } from "@/services/summary"

// Total Debt drilldown fields
const totalDebtFields: PivotField[] = [
  {
    id: "name",
    name: "Period",
    type: "dimension",
    dataType: "string",
    iconType: "tag",
  },
  {
    id: "amount",
    name: "Amount",
    type: "measure",
    dataType: "number",
    iconType: "hash",
    aggregation: "sum",
  },
  {
    id: "percentage",
    name: "Percentage",
    type: "measure",
    dataType: "number",
    iconType: "hash",
    aggregation: "avg",
  },
]

// Initial config for the modal
const initialTotalDebtPivotConfig: PivotConfiguration = {
  filters: [],
  rows: [totalDebtFields[0]],
  columns: [],
  values: [totalDebtFields[1], totalDebtFields[2]],
}

const TotalDebt = () => {
  const {
    data: { chart_data: data, commentary },
    isLoading,
  } = useCreditAndBalanceSheetTotalDebt()
  const [chartType, setChartType] = useState<ChartType>(CHART_TYPES[0])

  const [withCommentary] = useWithCommentary()

  return (
    <Card
      title="Total Debt"
      flashNumberLabel="(excl. Vidaskin)"
      filters={
        <ChartTypeSelect
          value={chartType}
          setValue={setChartType}
          disabled={isLoading}
        />
      }
      icons={
        <DrillDownModal
          data={data as unknown as DataRecord[]}
          title="Total Debt"
          isLoading={isLoading}
          availableFields={totalDebtFields}
          initialPivotConfig={initialTotalDebtPivotConfig}
          defaultChart={(config) => {
            const pivotData = generatePivotData(
              data as unknown as DataRecord[],
              config
            )

            const chartData = pivotData.map((row) => ({
              name: row.Period as string,
              amount: row.Amount as number,
              percentage: row.Percentage as number,
            })) as unknown as ProfitAndLossRevenueData[]

            return (
              <RevenueChart
                data={chartData}
                chartType={chartType}
                height="100%"
              />
            )
          }}
        />
      }
      commentary={withCommentary ? commentary : ""}
      isLoading={isLoading}
    >
      <RevenueChart data={data} chartType={chartType} />
    </Card>
  )
}

export default TotalDebt
