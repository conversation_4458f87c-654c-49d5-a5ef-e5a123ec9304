"use client"

import React, { useState } from "react"
import {
  Bar,
  BarChart,
  Cell,
  LabelList,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
} from "recharts"

import { CHART_TYPES, ChartType } from "@/types/chart"
import { BankNetDebtData } from "@/types/summary"
import { formatAbbreviatedCurrency } from "@/lib/number"
import { DataRecord, generatePivotData } from "@/lib/pivot"
import DrillDownModal from "@/components/advanced/DrillDownModal"
import { Card, CHART_HEIGHT } from "@/components/CardComponents"
import {
  ChartTypeSelect,
  WrappedTick,
  WrappedTooltip,
} from "@/components/ChartComponents"
import { useWithCommentary } from "@/components/layouts/ToggleCommentary"
import { useCreditAndBalanceSheetBankAndNetDebt } from "@/services/summary"

// Bank & Net Debt drilldown fields
const bankNetDebtFields = [
  {
    id: "name",
    name: "Period",
    type: "dimension" as const,
    dataType: "string" as const,
    iconType: "tag" as const,
  },
  {
    id: "bankDebt",
    name: "Bank Debt",
    type: "measure" as const,
    dataType: "number" as const,
    iconType: "hash" as const,
    aggregation: "sum" as const,
  },
  {
    id: "netDebt",
    name: "Net Debt",
    type: "measure" as const,
    dataType: "number" as const,
    iconType: "hash" as const,
    aggregation: "sum" as const,
  },
]

// Initial config for the modal
const initialBankNetDebtPivotConfig = {
  filters: [],
  rows: [bankNetDebtFields[0]],
  columns: [],
  values: [bankNetDebtFields[1], bankNetDebtFields[2]],
}

const BankNetDebt = () => {
  const {
    data: { chart_data: data, commentary },
    isLoading,
  } = useCreditAndBalanceSheetBankAndNetDebt()
  const [chartType, setChartType] = useState<ChartType>(CHART_TYPES[0])

  const [withCommentary] = useWithCommentary()

  return (
    <Card
      title="Bank & Net Debt"
      flashNumberLabel="(excl. Vidaskin)"
      filters={
        <ChartTypeSelect
          value={chartType}
          setValue={setChartType}
          disabled={isLoading}
        />
      }
      icons={
        <DrillDownModal
          data={data as unknown as DataRecord[]}
          title="Bank & Net Debt"
          isLoading={isLoading}
          availableFields={bankNetDebtFields}
          initialPivotConfig={initialBankNetDebtPivotConfig}
          defaultChart={(config) => {
            const pivotData = generatePivotData(
              data as unknown as DataRecord[],
              config
            )

            const chartData = pivotData.map((row) => ({
              name: row.Period as string,
              bankDebt: row["Bank Debt"] as number,
              netDebt: row["Net Debt"] as number,
            })) as unknown as BankNetDebtData[]

            return (
              <BankNetDebtChart
                data={chartData}
                chartType={chartType}
                height="100%"
              />
            )
          }}
        />
      }
      commentary={withCommentary ? commentary : ""}
      isLoading={isLoading}
    >
      <BankNetDebtChart data={data} chartType={chartType} />
    </Card>
  )
}

export default BankNetDebt

const MIN_BAR_HEIGHT = 8

const BankNetDebtChart = ({
  data,
  chartType,
  height = CHART_HEIGHT,
}: {
  data: BankNetDebtData[]
  chartType: ChartType
  height?: number | string
}) => (
  <ResponsiveContainer width="99%" height={height}>
    {chartType === "Bar" ? (
      <BarChart data={data} margin={{ top: 24 }}>
        <XAxis
          dataKey="name"
          tick={WrappedTick}
          tickLine={false}
          interval={0}
        />

        <Tooltip
          content={<WrappedTooltip />}
          formatter={(value: number) => formatAbbreviatedCurrency(value)}
        />

        <Bar dataKey="bankDebt" name="Bank Debt" fill="var(--chart-1)">
          <Cell fill="var(--chart-1)" />

          <LabelList
            content={({ value, x, y, width, height }) => {
              const isVisible =
                Number(value) != 0 && Math.abs(Number(height)) > MIN_BAR_HEIGHT

              if (!isVisible) return null

              return (
                <text
                  x={Number(x) + Number(width) / 2}
                  y={Number(y) + Number(height) / 2}
                  fill="white"
                  fontSize={12}
                  textAnchor="middle"
                  dominantBaseline="central"
                >
                  {formatAbbreviatedCurrency(Number(value))}
                </text>
              )
            }}
          />
        </Bar>

        <Bar dataKey="netDebt" name="Net Debt" fill="var(--chart-3)">
          <Cell fill="var(--chart-3)" />

          <LabelList
            content={({ value, x, y, width, height }) => {
              const isVisible =
                Number(value) != 0 && Math.abs(Number(height)) > MIN_BAR_HEIGHT

              if (!isVisible) return null

              return (
                <text
                  x={Number(x) + Number(width) / 2}
                  y={Number(y) + Number(height) / 2}
                  fill="white"
                  fontSize={12}
                  textAnchor="middle"
                  dominantBaseline="central"
                >
                  {formatAbbreviatedCurrency(Number(value))}
                </text>
              )
            }}
          />
        </Bar>
      </BarChart>
    ) : (
      <LineChart data={data} margin={{ top: 24, left: 24, right: 24 }}>
        <XAxis
          dataKey="name"
          tick={WrappedTick}
          tickLine={false}
          interval={0}
        />

        <Tooltip
          content={<WrappedTooltip />}
          formatter={(value: number) => formatAbbreviatedCurrency(value)}
        />

        <Line dataKey="bankDebt" name="Bank Debt" stroke="var(--chart-1)">
          <LabelList
            dataKey="bankDebt"
            position="top"
            fill="var(--foreground)"
            formatter={(value: number) => formatAbbreviatedCurrency(value)}
            fontSize={12}
          />
        </Line>

        <Line dataKey="netDebt" name="Net Debt" stroke="var(--chart-3)">
          <LabelList
            dataKey="netDebt"
            position="top"
            fill="var(--foreground)"
            formatter={(value: number) => formatAbbreviatedCurrency(value)}
            fontSize={12}
          />
        </Line>
      </LineChart>
    )}
  </ResponsiveContainer>
)
