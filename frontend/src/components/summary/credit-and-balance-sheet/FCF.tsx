"use client"

import React from "react"

import { CHART_TYPES } from "@/types/chart"
import { ProfitAndLossRevenueData } from "@/types/summary"
import {
  DataRecord,
  generatePivotData,
  PivotConfiguration,
  PivotField,
} from "@/lib/pivot"
import DrillDownModal from "@/components/advanced/DrillDownModal"
import { Card } from "@/components/CardComponents"
import { useWithCommentary } from "@/components/layouts/ToggleCommentary"
import { RevenueChart } from "@/components/summary/profit-and-loss/Revenue"
import { useCreditAndBalanceSheetFCF } from "@/services/summary"

// FCF drilldown fields
const fcfFields: PivotField[] = [
  {
    id: "name",
    name: "Period",
    type: "dimension",
    dataType: "string",
    iconType: "tag",
  },
  {
    id: "amount",
    name: "Amount",
    type: "measure",
    dataType: "number",
    iconType: "hash",
    aggregation: "sum",
  },
  {
    id: "percentage",
    name: "Percentage",
    type: "measure",
    dataType: "number",
    iconType: "hash",
    aggregation: "avg",
  },
]

// Initial config for the modal
const initialFCFPivotConfig: PivotConfiguration = {
  filters: [],
  rows: [fcfFields[0]],
  columns: [],
  values: [fcfFields[1], fcfFields[2]],
}

const FCF = () => {
  const {
    data: { chart_data: data, commentary },
    isLoading,
  } = useCreditAndBalanceSheetFCF()

  const [withCommentary] = useWithCommentary()

  return (
    <Card
      title="FCF"
      flashNumberLabel="(excl. Vidaskin)"
      icons={
        <DrillDownModal
          data={data as unknown as DataRecord[]}
          title="FCF"
          isLoading={isLoading}
          availableFields={fcfFields}
          initialPivotConfig={initialFCFPivotConfig}
          defaultChart={(config) => {
            const pivotData = generatePivotData(
              data as unknown as DataRecord[],
              config
            )

            const chartData = pivotData.map((row) => ({
              name: row.Period as string,
              amount: row.Amount as number,
              percentage: row.Percentage as number,
            })) as unknown as ProfitAndLossRevenueData[]

            return (
              <RevenueChart
                data={chartData}
                chartType={CHART_TYPES[0]}
                height="100%"
              />
            )
          }}
        />
      }
      commentary={withCommentary ? commentary : ""}
      isLoading={isLoading}
    >
      <RevenueChart data={data} chartType={CHART_TYPES[0]} />
    </Card>
  )
}

export default FCF
