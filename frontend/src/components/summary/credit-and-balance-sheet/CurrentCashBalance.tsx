"use client"

import React from "react"
import {
  <PERSON>,
  <PERSON><PERSON>hart,
  Cell,
  LabelList,
  ResponsiveContainer,
  XAxis,
} from "recharts"

import { WaterfallChartData } from "@/types/summary"
import { formatAbbreviatedCurrency } from "@/lib/number"
import { DataRecord, generatePivotData } from "@/lib/pivot"
import DrillDownModal from "@/components/advanced/DrillDownModal"
import { Card, CHART_HEIGHT } from "@/components/CardComponents"
import { WrappedTick } from "@/components/ChartComponents"
import { useWithCommentary } from "@/components/layouts/ToggleCommentary"
import { useCreditAndBalanceSheetCurrentCashBalance } from "@/services/summary"

// Current Cash Balance drilldown fields
const currentCashBalanceFields = [
  {
    id: "name",
    name: "Name",
    type: "dimension" as const,
    dataType: "string" as const,
    iconType: "tag" as const,
  },
  {
    id: "amount",
    name: "Amount",
    type: "measure" as const,
    dataType: "number" as const,
    iconType: "hash" as const,
    aggregation: "sum" as const,
  },
]

// Initial config for the modal
const initialCurrentCashBalancePivotConfig = {
  filters: [],
  rows: [currentCashBalanceFields[0]],
  columns: [],
  values: [currentCashBalanceFields[1]],
}

const CurrentCashBalance = () => {
  const {
    data: { chart_data: data, commentary },
    isLoading,
  } = useCreditAndBalanceSheetCurrentCashBalance()
  const [withCommentary] = useWithCommentary()

  return (
    <Card
      title="Current Cash Balance"
      flashNumberLabel="(excl. Vidaskin)"
      icons={
        <DrillDownModal
          data={data as unknown as DataRecord[]}
          title="Current Cash Balance"
          isLoading={isLoading}
          availableFields={currentCashBalanceFields}
          initialPivotConfig={initialCurrentCashBalancePivotConfig}
          defaultChart={(config) => {
            const pivotData = generatePivotData(
              data as unknown as DataRecord[],
              config
            )

            const chartData = data.filter((d) =>
              pivotData.some((p) => p.Name === d.name)
            )

            return <WaterfallChart data={chartData} height="100%" />
          }}
        />
      }
      commentary={withCommentary ? commentary : ""}
      isLoading={isLoading}
    >
      <WaterfallChart data={data} />
    </Card>
  )
}

export default CurrentCashBalance

export const WaterfallChart = ({
  data,
  height = CHART_HEIGHT,
}: {
  data: WaterfallChartData[]
  height?: number | string
}) => (
  <ResponsiveContainer width="99%" height={height}>
    <BarChart data={data} margin={{ top: 24 }}>
      <XAxis dataKey="name" tick={WrappedTick} interval={0} tickLine={false} />

      <Bar dataKey="remaining" stackId="a" fill="transparent" />

      <Bar dataKey="amount" name="Amount" stackId="a" fill="var(--chart-1)">
        {data.map((item, index) => {
          if (item.amount < 0) {
            return <Cell key={index} fill="var(--chart-3)" />
          }

          if (index === data.length - 1) {
            return <Cell key={index} fill="var(--chart-5)" />
          }

          return <Cell key={index} fill="var(--chart-1)" />
        })}

        <LabelList
          dataKey="amount"
          position="top"
          fill="var(--foreground)"
          formatter={(value: number) => formatAbbreviatedCurrency(value)}
          fontSize={12}
        />
      </Bar>
    </BarChart>
  </ResponsiveContainer>
)
