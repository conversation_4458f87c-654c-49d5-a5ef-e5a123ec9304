"use client"

import React from "react"
import {
  <PERSON><PERSON>ist,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  Tooltip,
  XAxis,
} from "recharts"

import { ChartDataPoint } from "@/types/summary"
import { formatAbbreviatedCurrency } from "@/lib/number"
import { DataRecord, generatePivotData } from "@/lib/pivot"
import DrillDownModal from "@/components/advanced/DrillDownModal"
import { Card, CHART_HEIGHT } from "@/components/CardComponents"
import { WrappedTick, WrappedTooltip } from "@/components/ChartComponents"
import { useWithCommentary } from "@/components/layouts/ToggleCommentary"
import { useCreditAndBalanceSheetOCF } from "@/services/summary"

// OCF drilldown fields
const ocfFields = [
  {
    id: "name",
    name: "Period",
    type: "dimension" as const,
    dataType: "string" as const,
    iconType: "tag" as const,
  },
  {
    id: "value",
    name: "Amount",
    type: "measure" as const,
    dataType: "number" as const,
    iconType: "hash" as const,
    aggregation: "sum" as const,
  },
]

// Initial config for the modal
const initialOCFPivotConfig = {
  filters: [],
  rows: [ocfFields[0]],
  columns: [],
  values: [ocfFields[1]],
}

const OCF = () => {
  const {
    data: { chart_data: data, commentary },
    isLoading,
  } = useCreditAndBalanceSheetOCF()
  const [withCommentary] = useWithCommentary()

  return (
    <Card
      title="OCF"
      flashNumberLabel="(excl. Vidaskin)"
      icons={
        <DrillDownModal
          data={data as unknown as DataRecord[]}
          title="OCF"
          isLoading={isLoading}
          availableFields={ocfFields}
          initialPivotConfig={initialOCFPivotConfig}
          defaultChart={(config) => {
            const pivotData = generatePivotData(
              data as unknown as DataRecord[],
              config
            )

            const chartData = pivotData.map((row) => ({
              name: row.Period as string,
              value: row.Amount as number,
            })) as unknown as ChartDataPoint[]

            return <OCFChart data={chartData} height="100%" />
          }}
        />
      }
      commentary={withCommentary ? commentary : ""}
      isLoading={isLoading}
    >
      <OCFChart data={data} />
    </Card>
  )
}

export default OCF

const OCFChart = ({
  data,
  height = CHART_HEIGHT,
}: {
  data: ChartDataPoint[]
  height?: number | string
}) => (
  <ResponsiveContainer width="99%" height={height}>
    <LineChart data={data} margin={{ top: 24, left: 24, right: 24 }}>
      <XAxis dataKey="name" tick={WrappedTick} tickLine={false} interval={0} />

      <Tooltip
        content={<WrappedTooltip />}
        formatter={(value: number) => formatAbbreviatedCurrency(value)}
      />

      <Line dataKey="value" name="Amount" stroke="var(--chart-1)">
        <LabelList
          dataKey="value"
          position="top"
          fill="var(--foreground)"
          formatter={(value: number) => formatAbbreviatedCurrency(value)}
          fontSize={12}
        />
      </Line>
    </LineChart>
  </ResponsiveContainer>
)
