"use client"

import React from "react"
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Label<PERSON>ist,
  Legend,
  Line,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"

import { PatientsData } from "@/types/summary"
import { formatAbbreviatedCurrency } from "@/lib/number"
import {
  DataRecord,
  generatePivotData,
  PivotConfiguration,
  PivotField,
} from "@/lib/pivot"
import DrillDownModal from "@/components/advanced/DrillDownModal"
import { Card, CHART_HEIGHT } from "@/components/CardComponents"
import { WrappedTick } from "@/components/ChartComponents"
import { useWithCommentary } from "@/components/layouts/ToggleCommentary"
import { useSummaryFilters } from "@/contexts/summary-filters"
import { usePatientsPatients } from "@/services/summary"

// Patients drilldown fields
const patientsFields: PivotField[] = [
  {
    id: "name",
    name: "Period",
    type: "dimension",
    dataType: "string",
    iconType: "tag",
  },
  {
    id: "barValue",
    name: "Patients",
    type: "measure",
    dataType: "number",
    iconType: "hash",
    aggregation: "sum",
  },
  {
    id: "lineValue",
    name: "Revenue per patient",
    type: "measure",
    dataType: "number",
    iconType: "hash",
    aggregation: "avg",
  },
]

// Initial config for the modal
const initialPatientsPivotConfig: PivotConfiguration = {
  filters: [],
  rows: [patientsFields[0]],
  columns: [],
  values: [patientsFields[1], patientsFields[2]],
}

const Patients = () => {
  const { filters } = useSummaryFilters()
  const {
    data: { chart_data: data, commentary },
    isLoading,
  } = usePatientsPatients(filters)

  const [withCommentary] = useWithCommentary()

  const labels: Record<keyof PatientsData, string> = {
    name: "Period",
    barValue: "Patients",
    lineValue: "Revenue per patient",
  }

  return (
    <Card
      title="Patients"
      flashNumberLabel="(excl. Vidaskin)"
      icons={
        <DrillDownModal
          data={data as unknown as DataRecord[]}
          title="Patients"
          isLoading={isLoading}
          availableFields={patientsFields}
          initialPivotConfig={initialPatientsPivotConfig}
          defaultChart={(config) => {
            const pivotData = generatePivotData(
              data as unknown as DataRecord[],
              config
            )

            const chartData = pivotData.map((row) => ({
              name: row.Period,
              barValue: row.Patients,
              lineValue: row["Revenue per patient"],
            })) as unknown as PatientsData[]

            return (
              <PatientsChart data={chartData} labels={labels} height="100%" />
            )
          }}
        />
      }
      commentary={withCommentary ? commentary : ""}
      isLoading={isLoading}
    >
      <PatientsChart data={data} labels={labels} />
    </Card>
  )
}

export default Patients

type PatientsDataWithLinePosition = PatientsData & {
  linePosition?: number
}

const addLinePosition = (
  data: PatientsData[]
): PatientsDataWithLinePosition[] => {
  const sums: number[] = data.map((entry) => entry.barValue || 0)

  const maxSum = Math.max(...sums)

  return data.map((entry) => {
    if (!entry.lineValue) return entry

    const linePosition =
      maxSum !== 0
        ? maxSum + (entry.lineValue / 10000) * maxSum + maxSum * 0.05
        : entry.lineValue

    return {
      ...entry,
      linePosition,
    }
  })
}

export const PatientsChart = ({
  data,
  labels,
  height = CHART_HEIGHT,
}: {
  data: PatientsData[]
  labels: Record<keyof PatientsData, string>
  height?: number | string
}) => {
  const chartData = addLinePosition(data)

  return (
    <ResponsiveContainer width="99%" height={height}>
      <ComposedChart data={chartData} margin={{ top: 24 }}>
        <XAxis
          dataKey="name"
          tick={WrappedTick}
          tickLine={false}
          interval={0}
        />

        <YAxis orientation="left" hide />

        <Tooltip
          formatter={(value, name, props) => {
            const row = props.payload
            if (name === labels.lineValue) {
              return [
                formatAbbreviatedCurrency(row.lineValue),
                labels.lineValue,
              ]
            }
            return [formatAbbreviatedCurrency(Number(value)), name]
          }}
        />

        <Legend verticalAlign="bottom" wrapperStyle={{ fontSize: 12 }} />

        <Line
          dataKey="linePosition"
          name={labels.lineValue}
          stroke="var(--color-chart-3)"
        >
          <LabelList
            dataKey="lineValue"
            position="top"
            fill="var(--foreground)"
            formatter={(value: number) => formatAbbreviatedCurrency(value)}
            fontSize={12}
          />
        </Line>

        <Bar
          dataKey="barValue"
          name={labels.barValue}
          stackId="a"
          fill="var(--chart-1)"
        >
          <LabelList
            dataKey="barValue"
            position="center"
            fill="white"
            formatter={(value: number) => formatAbbreviatedCurrency(value)}
            fontSize={12}
          />
        </Bar>
      </ComposedChart>
    </ResponsiveContainer>
  )
}
