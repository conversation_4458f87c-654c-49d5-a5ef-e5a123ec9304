"use client"

import React from "react"

import { PatientsData } from "@/types/summary"
import {
  DataRecord,
  generatePivotData,
  PivotConfiguration,
  PivotField,
} from "@/lib/pivot"
import DrillDownModal from "@/components/advanced/DrillDownModal"
import { Card } from "@/components/CardComponents"
import { useWithCommentary } from "@/components/layouts/ToggleCommentary"
import { PatientsChart } from "@/components/summary/patients/Patients"
import { useSummaryFilters } from "@/contexts/summary-filters"
import { usePatientsNewPatients } from "@/services/summary"

// New Patients drilldown fields
const newPatientsFields: PivotField[] = [
  {
    id: "name",
    name: "Period",
    type: "dimension",
    dataType: "string",
    iconType: "tag",
  },
  {
    id: "barValue",
    name: "Patients",
    type: "measure",
    dataType: "number",
    iconType: "hash",
    aggregation: "sum",
  },
  {
    id: "lineValue",
    name: "Revenue per patient",
    type: "measure",
    dataType: "number",
    iconType: "hash",
    aggregation: "avg",
  },
]

// Initial config for the modal
const initialNewPatientsPivotConfig: PivotConfiguration = {
  filters: [],
  rows: [newPatientsFields[0]],
  columns: [],
  values: [newPatientsFields[1], newPatientsFields[2]],
}

const NewPatients = () => {
  const { filters } = useSummaryFilters()
  const {
    data: { chart_data: data, commentary },
    isLoading,
  } = usePatientsNewPatients(filters)

  const [withCommentary] = useWithCommentary()

  const labels: Record<keyof PatientsData, string> = {
    name: "Period",
    barValue: "Patients",
    lineValue: "Revenue per patient",
  }

  return (
    <Card
      title="New Patients"
      flashNumberLabel="(excl. Vidaskin)"
      icons={
        <DrillDownModal
          data={data as unknown as DataRecord[]}
          title="New Patients"
          isLoading={isLoading}
          availableFields={newPatientsFields}
          initialPivotConfig={initialNewPatientsPivotConfig}
          defaultChart={(config) => {
            const pivotData = generatePivotData(
              data as unknown as DataRecord[],
              config
            )

            const chartData = pivotData.map((row) => ({
              name: row.Period,
              barValue: row.Patients,
              lineValue: row["Revenue per patient"],
            })) as unknown as PatientsData[]

            return (
              <PatientsChart data={chartData} labels={labels} height="100%" />
            )
          }}
        />
      }
      commentary={withCommentary ? commentary : ""}
      isLoading={isLoading}
    >
      <PatientsChart data={data} labels={labels} />
    </Card>
  )
}

export default NewPatients
