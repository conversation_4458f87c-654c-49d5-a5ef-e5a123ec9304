"use client"

import React from "react"

import { PatientsData } from "@/types/summary"
import { Card } from "@/components/CardComponents"
import { PatientsChart } from "@/components/summary/patients/Patients"
import { useSummaryFilters } from "@/contexts/summary-filters"
import { usePatientsDigitalMKT } from "@/services/summary"

const DigitalMKT = () => {
  const { filters } = useSummaryFilters()
  const {
    data: { chart_data: data },
    isLoading,
  } = usePatientsDigitalMKT(filters)

  const labels: Record<keyof PatientsData, string> = {
    name: "Period",
    barValue: "Patients",
    lineValue: "Revenue per patient",
  }

  return (
    <Card
      title="Digital MKT"
      flashNumberLabel="(excl. Vidaskin)"
      isLoading={isLoading}
    >
      <PatientsChart data={data} labels={labels} />
    </Card>
  )
}

export default DigitalMKT
