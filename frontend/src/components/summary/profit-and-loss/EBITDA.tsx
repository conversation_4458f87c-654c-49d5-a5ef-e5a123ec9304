"use client"

import React, { useState } from "react"

import { CHART_TYPES, ChartType } from "@/types/chart"
import {
  DataRecord,
  generatePivotData,
  PivotConfiguration,
  PivotField,
} from "@/lib/pivot"
import DrillDownModal from "@/components/advanced/DrillDownModal"
import { Card } from "@/components/CardComponents"
import { ChartTypeSelect } from "@/components/ChartComponents"
import { useWithCommentary } from "@/components/layouts/ToggleCommentary"
import { RevenueChart } from "@/components/summary/profit-and-loss/Revenue"
import { useSummaryFilters } from "@/contexts/summary-filters"
import { useProfitAndLossEBITDA } from "@/services/summary"

// EBITDA drilldown fields
const ebitdaFields: PivotField[] = [
  {
    id: "name",
    name: "Period",
    type: "dimension",
    dataType: "string",
    iconType: "tag",
  },
  {
    id: "amount",
    name: "Amount",
    type: "measure",
    dataType: "number",
    iconType: "hash",
    aggregation: "sum",
  },
  {
    id: "percentage",
    name: "Percentage",
    type: "measure",
    dataType: "number",
    iconType: "hash",
    aggregation: "avg",
  },
]

// Initial config for the modal
const initialEBITDAPivotConfig: PivotConfiguration = {
  filters: [],
  rows: [ebitdaFields[0]],
  columns: [],
  values: [ebitdaFields[1], ebitdaFields[2]],
}

const EBITDA = () => {
  const { filters } = useSummaryFilters()
  const {
    data: { chart_data: data, commentary },
    isLoading,
  } = useProfitAndLossEBITDA(filters)
  const [chartType, setChartType] = useState<ChartType>(CHART_TYPES[0])

  const [withCommentary] = useWithCommentary()

  return (
    <Card
      title="EBITDA"
      flashNumberLabel="(excl. Vidaskin)"
      filters={
        <ChartTypeSelect
          value={chartType}
          setValue={setChartType}
          disabled={isLoading}
        />
      }
      icons={
        <DrillDownModal
          data={data as unknown as DataRecord[]}
          title="EBITDA"
          isLoading={isLoading}
          availableFields={ebitdaFields}
          initialPivotConfig={initialEBITDAPivotConfig}
          defaultChart={(config) => {
            const pivotData = generatePivotData(
              data as unknown as DataRecord[],
              config
            )
            // Transform the data to match the expected format for RevenueChart
            const chartData = pivotData.map((row) => ({
              name: row.Period as string,
              amount: row.Amount as number,
              percentage: row.Percentage as number,
            }))

            return (
              <RevenueChart
                data={chartData}
                chartType={chartType}
                height="100%"
              />
            )
          }}
        />
      }
      commentary={withCommentary ? commentary : ""}
      isLoading={isLoading}
    >
      <RevenueChart data={data} chartType={chartType} />
    </Card>
  )
}

export default EBITDA
