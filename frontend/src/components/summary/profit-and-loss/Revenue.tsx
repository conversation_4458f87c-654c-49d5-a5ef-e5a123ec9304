"use client"

import React, { useState } from "react"
import {
  Bar,
  BarChart,
  Cell,
  LabelList,
  LabelProps,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
} from "recharts"

import { CHART_TYPES, ChartType } from "@/types/chart"
import { ProfitAndLossRevenueData } from "@/types/summary"
import { formatAbbreviatedCurrency } from "@/lib/number"
import { DataRecord, PivotConfiguration, PivotField } from "@/lib/pivot"
import { cn } from "@/lib/utils"
import DrillDownModal from "@/components/advanced/DrillDownModal"
import { Card, CHART_HEIGHT } from "@/components/CardComponents"
import {
  ChartTypeSelect,
  WrappedTick,
  WrappedTooltip,
} from "@/components/ChartComponents"
import { useWithCommentary } from "@/components/layouts/ToggleCommentary"
import { useSummaryFilters } from "@/contexts/summary-filters"
import {
  useProfitAndLossRevenue,
  useProfitAndLossRevenueDetailed,
} from "@/services/summary"

// Enhanced Revenue drilldown fields with comprehensive dimensions and measures
const revenueFields: PivotField[] = [
  // Date/Time Dimensions
  {
    id: "transaction_date",
    name: "Transaction Date",
    type: "dimension",
    dataType: "date",
    iconType: "calendar",
  },
  {
    id: "year",
    name: "Year",
    type: "dimension",
    dataType: "number",
    iconType: "calendar",
  },
  {
    id: "month",
    name: "Month",
    type: "dimension",
    dataType: "number",
    iconType: "calendar",
  },
  {
    id: "month_name",
    name: "Month Name",
    type: "dimension",
    dataType: "string",
    iconType: "calendar",
  },
  {
    id: "quarter",
    name: "Quarter",
    type: "dimension",
    dataType: "string",
    iconType: "calendar",
  },
  {
    id: "day_of_week",
    name: "Day of Week",
    type: "dimension",
    dataType: "string",
    iconType: "calendar",
  },

  // Account Dimensions
  {
    id: "account_code",
    name: "Account Code",
    type: "dimension",
    dataType: "string",
    iconType: "tag",
  },
  {
    id: "account_name",
    name: "Account Name",
    type: "dimension",
    dataType: "string",
    iconType: "tag",
  },
  {
    id: "account_type",
    name: "Account Type",
    type: "dimension",
    dataType: "string",
    iconType: "tag",
  },

  // Entity/Organization Dimensions
  {
    id: "entity_name",
    name: "Entity Name",
    type: "dimension",
    dataType: "string",
    iconType: "tag",
  },

  // Transaction Dimensions
  {
    id: "transaction_type",
    name: "Transaction Type",
    type: "dimension",
    dataType: "string",
    iconType: "tag",
  },
  {
    id: "transaction_status",
    name: "Transaction Status",
    type: "dimension",
    dataType: "string",
    iconType: "tag",
  },
  {
    id: "memo",
    name: "Memo",
    type: "dimension",
    dataType: "string",
    iconType: "tag",
  },

  // Reference Dimensions
  {
    id: "reference_1",
    name: "Reference 1",
    type: "dimension",
    dataType: "string",
    iconType: "tag",
  },
  {
    id: "reference_2",
    name: "Reference 2",
    type: "dimension",
    dataType: "string",
    iconType: "tag",
  },
  {
    id: "base_reference",
    name: "Base Reference",
    type: "dimension",
    dataType: "string",
    iconType: "tag",
  },

  // Currency Dimensions
  {
    id: "transaction_currency",
    name: "Transaction Currency",
    type: "dimension",
    dataType: "string",
    iconType: "tag",
  },
  {
    id: "reporting_currency",
    name: "Reporting Currency",
    type: "dimension",
    dataType: "string",
    iconType: "tag",
  },

  // Financial Measures
  {
    id: "amount",
    name: "Net Amount",
    type: "measure",
    dataType: "number",
    iconType: "hash",
    aggregation: "sum",
  },
  {
    id: "debit_amount",
    name: "Debit Amount",
    type: "measure",
    dataType: "number",
    iconType: "hash",
    aggregation: "sum",
  },
  {
    id: "credit_amount",
    name: "Credit Amount",
    type: "measure",
    dataType: "number",
    iconType: "hash",
    aggregation: "sum",
  },

  // Backward compatibility fields
  {
    id: "name",
    name: "Period",
    type: "dimension",
    dataType: "string",
    iconType: "tag",
  },
  {
    id: "percentage",
    name: "Percentage",
    type: "measure",
    dataType: "number",
    iconType: "hash",
    aggregation: "avg",
  },
]

// Initial config for the modal
const initialRevenuePivotConfig: PivotConfiguration = {
  filters: [],
  rows: [revenueFields[0]],
  columns: [],
  values: [revenueFields[1], revenueFields[2]],
}

const Revenue = () => {
  const { filters } = useSummaryFilters()
  const {
    data: { chart_data: data, commentary },
    isLoading,
  } = useProfitAndLossRevenue(filters)

  // Get detailed data for drill-down
  const { data: detailedData, isLoading: isDetailedLoading } =
    useProfitAndLossRevenueDetailed(filters)

  const [chartType, setChartType] = useState<ChartType>(CHART_TYPES[0])

  const [withCommentary] = useWithCommentary()

  return (
    <Card
      title="Revenue"
      flashNumberLabel="(excl. Vidaskin)"
      filters={
        <ChartTypeSelect
          value={chartType}
          setValue={setChartType}
          disabled={isLoading}
        />
      }
      icons={
        <DrillDownModal
          data={detailedData as unknown as DataRecord[]}
          title="Revenue"
          isLoading={isDetailedLoading}
          availableFields={revenueFields}
          initialPivotConfig={initialRevenuePivotConfig}
          defaultChart={(_config) => {
            // For the default chart, use the original aggregated data
            const chartData = data.map((item) => ({
              name: item.name,
              amount: item.amount,
              percentage: item.percentage,
            }))

            return (
              <RevenueChart
                data={chartData}
                chartType={chartType}
                height="100%"
              />
            )
          }}
        />
      }
      commentary={withCommentary ? commentary : ""}
      isLoading={isLoading}
    >
      <RevenueChart data={data} chartType={chartType} />
    </Card>
  )
}

export default Revenue

const MIN_BAR_HEIGHT = 8

export const RevenueChart = ({
  data,
  chartType,
  height = CHART_HEIGHT,
}: {
  data: ProfitAndLossRevenueData[]
  chartType: ChartType
  height?: number | string
}) => (
  <ResponsiveContainer width="99%" height={height}>
    {chartType === "Bar" ? (
      <BarChart data={data} margin={{ top: 32 }}>
        <XAxis
          dataKey="name"
          tick={WrappedTick}
          tickLine={false}
          interval={0}
        />

        <Tooltip
          content={<WrappedTooltip />}
          formatter={(value: number) => formatAbbreviatedCurrency(value)}
        />

        <Bar dataKey="amount" name="Amount" fill="var(--chart-1)">
          {data.map((_, index) => (
            <Cell key={`cell-${index}`} fill="var(--chart-1)" />
          ))}

          <LabelList
            content={({ x, y, width, height, index }) => {
              const value = data?.[index || 0]?.["amount"] || 0

              const isVisible =
                Number(value) != 0 && Math.abs(Number(height)) > MIN_BAR_HEIGHT

              if (!isVisible) return null

              return (
                <text
                  x={Number(x) + Number(width) / 2}
                  y={Number(y) + Number(height) / 2}
                  fill="white"
                  fontSize={12}
                  textAnchor="middle"
                  dominantBaseline="central"
                >
                  {formatAbbreviatedCurrency(Number(value))}
                </text>
              )
            }}
          />

          <LabelList
            dataKey="percentage"
            content={(props: LabelProps) =>
              renderPercentageLabel(chartType, props)
            }
          />
        </Bar>
      </BarChart>
    ) : (
      <LineChart data={data} margin={{ top: 24, left: 24, right: 24 }}>
        <XAxis
          dataKey="name"
          tick={WrappedTick}
          tickLine={false}
          interval={0}
        />

        <Tooltip
          content={<WrappedTooltip />}
          formatter={(value: number) => formatAbbreviatedCurrency(value)}
        />

        <Line dataKey="amount" name="Amount" stroke="var(--chart-1)">
          <LabelList
            dataKey="amount"
            position="bottom"
            fill="var(--chart-1)"
            formatter={(value: number) => formatAbbreviatedCurrency(value)}
            fontSize={12}
          />

          <LabelList
            dataKey="percentage"
            content={(props: LabelProps) =>
              renderPercentageLabel(chartType, props)
            }
          />
        </Line>
      </LineChart>
    )}
  </ResponsiveContainer>
)

const renderPercentageLabel = (chartType: ChartType, props: LabelProps) => {
  const { x, y, width, value } = props

  if (!value) return null

  const posX = chartType === "Bar" ? Number(x) + Number(width) / 2 : Number(x)

  return (
    <g>
      <rect
        className={cn(Number(value) > 0 ? "fill-green-100" : "fill-red-100")}
        x={posX - 25}
        y={Number(y) - 32}
        width={50}
        height={20}
        rx={12}
        ry={12}
      />
      <text
        className={cn(
          "text-xs font-medium",
          Number(value) > 0 ? "fill-green-600" : "fill-red-600"
        )}
        x={posX}
        y={Number(y) - 21}
        textAnchor="middle"
        dominantBaseline="middle"
      >
        {Number(value) > 0 ? "+" : ""}
        {value}%
      </text>
    </g>
  )
}
