"use client"

import React, { useState } from "react"
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  LabelList,
  LabelProps,
  Legend,
  ResponsiveContainer,
  Tooltip,
  XAxis,
} from "recharts"

import { ProfitAndLossRevenueBreakdownData } from "@/types/summary"
import { formatAbbreviatedCurrency } from "@/lib/number"
import {
  DataRecord,
  generatePivotData,
  PivotConfiguration,
  PivotField,
} from "@/lib/pivot"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import DrillDownModal from "@/components/advanced/DrillDownModal"
import { Card, CHART_HEIGHT } from "@/components/CardComponents"
import {
  CUSTOM_CHART_COLORS,
  WrappedTick,
  WrappedTooltip,
} from "@/components/ChartComponents"
import { useWithCommentary } from "@/components/layouts/ToggleCommentary"
import { useSummaryFilters } from "@/contexts/summary-filters"
import { useProfitAndLossRevenueBreakdown } from "@/services/summary"

const CHART_TYPES = ["By Segment"] as const
type ChartType = (typeof CHART_TYPES)[number]

// Revenue Breakdown drilldown fields
const getRevenueBreakdownFields = (
  data: ProfitAndLossRevenueBreakdownData[]
): PivotField[] => {
  const baseFields: PivotField[] = [
    {
      id: "name",
      name: "Period",
      type: "dimension",
      dataType: "string",
      iconType: "tag",
    },
    {
      id: "total",
      name: "Total",
      type: "measure",
      dataType: "number",
      iconType: "hash",
      aggregation: "sum",
    },
  ]

  if (!data || data.length === 0) {
    return baseFields
  }

  const firstDataItem = data[0]
  const dynamicFields: PivotField[] = Object.keys(firstDataItem)
    .filter((key) => key !== "name" && key !== "total")
    .map((key) => ({
      id: key,
      name: key,
      type: "measure",
      dataType: "number",
      iconType: "hash",
      aggregation: "sum",
    }))

  return [...baseFields, ...dynamicFields]
}

const getInitialRevenueBreakdownPivotConfig = (
  data: ProfitAndLossRevenueBreakdownData[]
): PivotConfiguration => {
  const fields = getRevenueBreakdownFields(data)
  return {
    filters: [],
    rows: [fields[0]],
    columns: [],
    values: fields.slice(1),
  }
}

const RevenueBreakdown = () => {
  const { filters } = useSummaryFilters()
  const [chartType, setChartType] = useState<ChartType>(CHART_TYPES[0])

  const {
    data: { chart_data: data, commentary },
    isLoading,
  } = useProfitAndLossRevenueBreakdown({
    type: chartType.toLowerCase().replaceAll(" ", "-"),
    filters,
  })

  const [withCommentary] = useWithCommentary()

  return (
    <Card
      title="Revenue Breakdown"
      flashNumberLabel="(excl. Vidaskin)"
      filters={
        <ChartTypeSelect
          value={chartType}
          setValue={setChartType}
          disabled={isLoading}
        />
      }
      icons={
        <DrillDownModal
          data={data as unknown as DataRecord[]}
          title="Revenue Breakdown"
          isLoading={isLoading}
          availableFields={getRevenueBreakdownFields(data)}
          initialPivotConfig={getInitialRevenueBreakdownPivotConfig(data)}
          defaultChart={(config) => {
            const pivotData = generatePivotData(
              data as unknown as DataRecord[],
              config
            )

            // Transform the data to match the expected format
            const chartData = pivotData.map(({ Period, Total, ...row }) => {
              void Total

              if (!config.values.some((item) => item.id === "total")) {
                return {
                  ...row,
                  name: Period,
                  total: 0,
                }
              }

              const calculatedTotal = Object.values(row).reduce(
                (sum, value) =>
                  Number(sum) + (typeof value === "number" ? value : 0),
                0
              )

              return {
                ...row,
                name: Period,
                total: calculatedTotal,
              }
            }) as ProfitAndLossRevenueBreakdownData[]

            return <RevenueBreakdownChart data={chartData} height="100%" />
          }}
        />
      }
      commentary={withCommentary ? commentary : ""}
      isLoading={isLoading}
    >
      <RevenueBreakdownChart data={data} />
    </Card>
  )
}

export default RevenueBreakdown

const RevenueBreakdownChart = ({
  data,
  height = CHART_HEIGHT,
}: {
  data: ProfitAndLossRevenueBreakdownData[]
  height?: number | string
}) => (
  <ResponsiveContainer width="99%" height={height}>
    <BarChart
      data={data}
      margin={{
        top: 24,
      }}
    >
      <XAxis dataKey="name" tick={WrappedTick} tickLine={false} interval={0} />

      <Tooltip
        content={<WrappedTooltip />}
        formatter={(value: number) => formatAbbreviatedCurrency(value)}
      />

      <Legend
        iconType="circle"
        iconSize={8}
        verticalAlign="top"
        wrapperStyle={{ fontSize: 12, top: 0 }}
      />

      {Object.keys(data?.[0] || {})
        .filter((key) => key !== "name" && key !== "total")
        .map((key, index) => (
          <Bar
            key={key}
            dataKey={key}
            name={key}
            stackId="a"
            fill={
              index < 5
                ? `var(--chart-${index + 1})`
                : CUSTOM_CHART_COLORS[index - 5] || "var(--chart-1)"
            }
          >
            <LabelList
              dataKey={key}
              content={renderLabelWithPercentage(data, key)}
            />
          </Bar>
        ))}

      <Bar dataKey="" stackId="a" fill="transparent">
        <LabelList
          dataKey="name"
          content={(props: LabelProps) => {
            const { x, y, width, value } = props
            const item = data.find((d) => d.name === value)
            if (!item || item.total === 0) return null

            return (
              <g>
                <text
                  className="text-sm"
                  x={Number(x) + Number(width) / 2}
                  y={Number(y) - 12}
                  textAnchor="middle"
                  dominantBaseline="middle"
                >
                  {formatAbbreviatedCurrency(item.total)}
                </text>
              </g>
            )
          }}
        />
      </Bar>
    </BarChart>
  </ResponsiveContainer>
)

const ChartTypeSelect = ({
  value,
  setValue,
  disabled,
}: {
  value: ChartType
  setValue: React.Dispatch<React.SetStateAction<ChartType>>
  disabled?: boolean
}) => (
  <Select
    value={value}
    onValueChange={(val) => setValue(val as ChartType)}
    disabled={disabled}
  >
    <SelectTrigger size="sm">
      <SelectValue placeholder="Select" />
    </SelectTrigger>
    <SelectContent>
      {CHART_TYPES.map((type) => (
        <SelectItem key={type} value={type}>
          {type}
        </SelectItem>
      ))}
    </SelectContent>
  </Select>
)

const MIN_BAR_HEIGHT = 8

const renderLabelWithPercentage = (
  data: ProfitAndLossRevenueBreakdownData[],
  dataKey: keyof ProfitAndLossRevenueBreakdownData
) => {
  const LabelComponent = (props: LabelProps) => {
    const { x, y, width = 0, height = 0, index, value } = props

    if (
      typeof index !== "number" ||
      typeof value !== "number" ||
      x === undefined ||
      y === undefined
    ) {
      return null
    }

    const isVisible =
      Number(value) != 0 && Math.abs(Number(height)) > MIN_BAR_HEIGHT

    if (!isVisible) return null

    const item = data[index]
    const total = item.total

    if (!total || total === 0)
      return (
        <text
          x={Number(x) + Number(width) / 2}
          y={Number(y) + Number(height) / 2}
          fill="white"
          textAnchor="middle"
          dominantBaseline="middle"
          fontSize={12}
        >
          {formatAbbreviatedCurrency(value)}
        </text>
      )

    const percentage = ((value / total) * 100).toFixed(0)

    return (
      <text
        x={Number(x) + Number(width) / 2}
        y={Number(y) + Number(height) / 2}
        fill="white"
        textAnchor="middle"
        dominantBaseline="middle"
        fontSize={12}
      >
        {formatAbbreviatedCurrency(value)} ({percentage}%)
      </text>
    )
  }
  LabelComponent.displayName = `LabelWithPercentage_${String(dataKey)}`

  return LabelComponent
}
