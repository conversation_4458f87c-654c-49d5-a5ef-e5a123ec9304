export type WithCommentary<T> = {
  chart_data: T[]
  commentary: string
}

export const DEFAULT_CHART_DATA_WITH_COMMENTARY = {
  chart_data: [],
  commentary: "",
}

export interface ProfitAndLossRevenueData {
  name: string
  amount: number
  percentage: number
}

export type ProfitAndLossRevenueBreakdownData = StackedBarData

export interface BankNetDebtData {
  name: string
  bankDebt: number
  netDebt: number
}

export interface WaterfallChartData {
  name: string
  amount: number
  remaining: number
}

export interface PatientsData {
  name: string
  barValue: number
  lineValue?: number
}

export type StackedBarData = {
  name: string
  total: number
} & {
  [key: string]: number
}

export interface OrgChartData {
  name: string
  children?: OrgChartData[]
}

export interface ChartDataPoint {
  name: string
  value: number
}

export interface OperationsAndHROverviewMetric {
  value: number
  percentage: number
  chart_data: ChartDataPoint[]
}

export interface OperationsAndHROverviewData {
  revenue_per_fte: OperationsAndHROverviewMetric
  revenue_per_doctor: OperationsAndHROverviewMetric
  employees: OperationsAndHROverviewMetric
  positive_attrition_rate: OperationsAndHROverviewMetric
  negative_attrition_rate: OperationsAndHROverviewMetric
}

export interface BusinessOverviewMetric {
  value: number
  percentage: number
  chart_data: ChartDataPoint[]
  additional_info?: {
    label: string
    value: string
  }
}

export interface BusinessOverviewData {
  gross_revenue: BusinessOverviewMetric
  ebitda: BusinessOverviewMetric
  bank_debt: BusinessOverviewMetric
  cash_balance: BusinessOverviewMetric
  fte: BusinessOverviewMetric
  net_profit: BusinessOverviewMetric
}
