"use client"

import useSWRImmutable from "swr/immutable"

import { CustomResponse } from "@/types/response"
import {
  BankNetDebtData,
  BusinessOverviewData,
  ChartDataPoint,
  DEFAULT_CHART_DATA_WITH_COMMENTARY,
  OperationsAndHROverviewData,
  OrgChartData,
  PatientsData,
  ProfitAndLossRevenueBreakdownData,
  ProfitAndLossRevenueData,
  StackedBarData,
  WaterfallChartData,
  WithCommentary,
} from "@/types/summary"
import { createFetcher } from "@/services/fetcher"

// Filter types
export interface SummaryFilters {
  currency?: string
  year?: number
  last_n_years?: number
  segment?: string[]
  sub_segment?: string[]
  clinic?: string[]
  doctor?: string[]
  service?: string[]
}

// Helper function to build query string from filters
const buildQueryString = (filters: SummaryFilters): string => {
  const params = new URLSearchParams()

  // Add basic filters
  if (filters.currency) params.append("currency", filters.currency)
  if (filters.year) params.append("year", filters.year.toString())
  if (typeof filters.last_n_years === "number")
    params.append("last_n_years", filters.last_n_years.toString())

  // Add array filters
  if (filters.segment?.length) {
    filters.segment.forEach((value) => params.append("segment", value))
  }
  if (filters.sub_segment?.length) {
    filters.sub_segment.forEach((value) => params.append("sub_segment", value))
  }
  if (filters.clinic?.length) {
    filters.clinic.forEach((value) => params.append("clinic", value))
  }
  if (filters.doctor?.length) {
    filters.doctor.forEach((value) => params.append("doctor", value))
  }
  if (filters.service?.length) {
    filters.service.forEach((value) => params.append("service", value))
  }

  params.append("refresh", "3")

  return params.toString()
}

export const useProfitAndLossOverview = (filters: SummaryFilters = {}) => {
  const queryString = buildQueryString({
    currency: "SGD",
    year: 2025,
    last_n_years: 4,
    ...filters,
  })
  const url = `/summary/profit-and-loss/overview/?${queryString}`

  const snapshot = useSWRImmutable<CustomResponse<BusinessOverviewData>>(
    url,
    createFetcher
  )

  return { ...snapshot, data: snapshot.data?.data }
}

export const useProfitAndLossRevenue = (filters: SummaryFilters = {}) => {
  const queryString = buildQueryString({
    currency: "SGD",
    year: 2025,
    last_n_years: 4,
    ...filters,
  })
  const url = `/summary/profit-and-loss/revenue/chart/?${queryString}`

  const snapshot = useSWRImmutable<
    CustomResponse<WithCommentary<ProfitAndLossRevenueData>>
  >(url, createFetcher)

  return {
    ...snapshot,
    data: snapshot.data?.data || DEFAULT_CHART_DATA_WITH_COMMENTARY,
  }
}

export const useProfitAndLossRevenueDetailed = (
  filters: SummaryFilters = {}
) => {
  const queryString = buildQueryString({
    currency: "SGD",
    year: 2025,
    last_n_years: 4,
    ...filters,
  })
  const url = `/summary/profit-and-loss/revenue/detailed/?${queryString}`

  const snapshot = useSWRImmutable<CustomResponse<any[]>>(url, createFetcher)

  return {
    ...snapshot,
    data: snapshot.data?.data || [],
  }
}

export const useProfitAndLossEBITDA = (filters: SummaryFilters = {}) => {
  const queryString = buildQueryString({
    currency: "SGD",
    year: 2025,
    last_n_years: 4,
    ...filters,
  })
  const url = `/summary/profit-and-loss/ebitda/chart/?${queryString}`

  const snapshot = useSWRImmutable<
    CustomResponse<WithCommentary<ProfitAndLossRevenueData>>
  >(url, createFetcher)

  return {
    ...snapshot,
    data: snapshot.data?.data || DEFAULT_CHART_DATA_WITH_COMMENTARY,
  }
}

export const useProfitAndLossNetProfit = (filters: SummaryFilters = {}) => {
  const queryString = buildQueryString({
    currency: "SGD",
    year: 2025,
    last_n_years: 4,
    ...filters,
  })
  const url = `/summary/profit-and-loss/net-profit/chart/?${queryString}`

  const snapshot = useSWRImmutable<
    CustomResponse<WithCommentary<ProfitAndLossRevenueData>>
  >(url, createFetcher)

  return {
    ...snapshot,
    data: snapshot.data?.data || DEFAULT_CHART_DATA_WITH_COMMENTARY,
  }
}

export const useProfitAndLossRevenueBreakdown = ({
  type,
  filters = {},
}: {
  type: string
  filters?: SummaryFilters
}) => {
  const queryString = buildQueryString({
    currency: "SGD",
    year: 2025,
    last_n_years: 4,
    ...filters,
  })
  const url = `/summary/profit-and-loss/revenue-breakdown/${type}/chart/?${queryString}`

  const snapshot = useSWRImmutable<
    CustomResponse<WithCommentary<ProfitAndLossRevenueBreakdownData>>
  >(url, createFetcher)

  return {
    ...snapshot,
    data: snapshot.data?.data || DEFAULT_CHART_DATA_WITH_COMMENTARY,
  }
}

export const useCreditAndBalanceSheetTotalDebt = (
  filters: SummaryFilters = {}
) => {
  const queryString = buildQueryString({
    currency: "SGD",
    year: 2025,
    last_n_years: 4,
    ...filters,
  })
  const url = `/summary/credit-and-balance-sheet/total-debt/chart/?${queryString}`

  const snapshot = useSWRImmutable<
    CustomResponse<WithCommentary<ProfitAndLossRevenueData>>
  >(url, createFetcher)

  return {
    ...snapshot,
    data: snapshot.data?.data || DEFAULT_CHART_DATA_WITH_COMMENTARY,
  }
}

export const useCreditAndBalanceSheetBankAndNetDebt = (
  filters: SummaryFilters = {}
) => {
  const queryString = buildQueryString({
    currency: "SGD",
    year: 2025,
    last_n_years: 4,
    ...filters,
  })
  const url = `/summary/credit-and-balance-sheet/bank-and-net-debt/chart/?${queryString}`

  const snapshot = useSWRImmutable<
    CustomResponse<WithCommentary<BankNetDebtData>>
  >(url, createFetcher)

  return {
    ...snapshot,
    data: snapshot.data?.data || DEFAULT_CHART_DATA_WITH_COMMENTARY,
  }
}

export const useCreditAndBalanceSheetCurrentCashBalance = (
  filters: SummaryFilters = {}
) => {
  const queryString = buildQueryString({
    currency: "SGD",
    year: 2025,
    ...filters,
  })
  const url = `/summary/credit-and-balance-sheet/current-cash-balance/chart/?${queryString}`

  const snapshot = useSWRImmutable<
    CustomResponse<WithCommentary<WaterfallChartData>>
  >(url, createFetcher)

  return {
    ...snapshot,
    data: snapshot.data?.data || DEFAULT_CHART_DATA_WITH_COMMENTARY,
  }
}

export const useCreditAndBalanceSheetOCF = (filters: SummaryFilters = {}) => {
  const queryString = buildQueryString({
    currency: "SGD",
    year: 2025,
    last_n_years: 4,
    ...filters,
  })
  const url = `/summary/credit-and-balance-sheet/ocf/chart/?${queryString}`

  const snapshot = useSWRImmutable<
    CustomResponse<WithCommentary<ChartDataPoint>>
  >(url, createFetcher)

  return {
    ...snapshot,
    data: snapshot.data?.data || DEFAULT_CHART_DATA_WITH_COMMENTARY,
  }
}

export const useCreditAndBalanceSheetFCF = (filters: SummaryFilters = {}) => {
  const queryString = buildQueryString({
    currency: "SGD",
    year: 2025,
    last_n_years: 4,
    ...filters,
  })
  const url = `/summary/credit-and-balance-sheet/fcf/chart/?${queryString}`

  const snapshot = useSWRImmutable<
    CustomResponse<WithCommentary<ProfitAndLossRevenueData>>
  >(url, createFetcher)

  return {
    ...snapshot,
    data: snapshot.data?.data || DEFAULT_CHART_DATA_WITH_COMMENTARY,
  }
}

export const useOperationsAndHROverview = (filters: SummaryFilters = {}) => {
  const queryString = buildQueryString({
    currency: "SGD",
    year: 2025,
    ...filters,
  })
  const url = `/summary/operations-and-hr/overview/?${queryString}`

  const snapshot = useSWRImmutable<CustomResponse<OperationsAndHROverviewData>>(
    url,
    createFetcher
  )

  return { ...snapshot, data: snapshot.data?.data }
}

export const useOperationsAndHRClinics = (filters: SummaryFilters = {}) => {
  const queryString = buildQueryString({
    currency: "SGD",
    year: 2025,
    last_n_years: 4,
    ...filters,
  })
  const url = `/summary/operations-and-hr/clinics/chart/?${queryString}`

  const snapshot = useSWRImmutable<
    CustomResponse<WithCommentary<StackedBarData>>
  >(url, createFetcher)

  return {
    ...snapshot,
    data: snapshot.data?.data || DEFAULT_CHART_DATA_WITH_COMMENTARY,
  }
}

export const useOperationsAndHRDoctors = (filters: SummaryFilters = {}) => {
  const queryString = buildQueryString({
    year: 2025,
    last_n_years: 4,
    ...filters,
  })
  const url = `/summary/operations-and-hr/doctors/chart/?${queryString}`

  const snapshot = useSWRImmutable<
    CustomResponse<WithCommentary<StackedBarData>>
  >(url, createFetcher)

  return {
    ...snapshot,
    data: snapshot.data?.data || DEFAULT_CHART_DATA_WITH_COMMENTARY,
  }
}

export const useOperationsAndHRFTE = (filters: SummaryFilters = {}) => {
  const queryString = buildQueryString({
    year: 2025,
    last_n_years: 4,
    ...filters,
  })
  const url = `/summary/operations-and-hr/fte/chart/?${queryString}`

  const snapshot = useSWRImmutable<
    CustomResponse<WithCommentary<StackedBarData>>
  >(url, createFetcher)

  return {
    ...snapshot,
    data: snapshot.data?.data || DEFAULT_CHART_DATA_WITH_COMMENTARY,
  }
}

export const useOperationsAndHRHeadcount = ({
  type,
  filters = {},
}: {
  type: string
  filters: SummaryFilters
}) => {
  const queryString = buildQueryString({
    year: 2025,
    last_n_years: 4,
    ...filters,
  })
  const url = `/summary/operations-and-hr/headcount/chart/?type=${type}&${queryString}`

  const snapshot = useSWRImmutable<
    CustomResponse<WithCommentary<StackedBarData>>
  >(url, createFetcher)

  return {
    ...snapshot,
    data: snapshot.data?.data || DEFAULT_CHART_DATA_WITH_COMMENTARY,
  }
}

export const useOperationsAndHROrgChart = () => {
  const url = "/summary/operations-and-hr/org-chart/"

  const snapshot = useSWRImmutable<CustomResponse<OrgChartData>>(
    url,
    createFetcher
  )

  return { ...snapshot, data: snapshot.data?.data || { name: "SMG" } }
}

export const usePatientsPatients = (filters: SummaryFilters = {}) => {
  const queryString = buildQueryString({
    year: 2025,
    last_n_years: 4,
    ...filters,
  })
  const url = `/summary/patients/patients/chart/?${queryString}`

  const snapshot = useSWRImmutable<
    CustomResponse<WithCommentary<PatientsData>>
  >(url, createFetcher)

  return {
    ...snapshot,
    data: snapshot.data?.data || DEFAULT_CHART_DATA_WITH_COMMENTARY,
  }
}

export const usePatientsNewPatients = (filters: SummaryFilters = {}) => {
  const queryString = buildQueryString({
    year: 2025,
    last_n_years: 4,
    ...filters,
  })
  const url = `/summary/patients/new-patients/chart/?${queryString}`

  const snapshot = useSWRImmutable<
    CustomResponse<WithCommentary<PatientsData>>
  >(url, createFetcher)

  return {
    ...snapshot,
    data: snapshot.data?.data || DEFAULT_CHART_DATA_WITH_COMMENTARY,
  }
}

export const usePatientsDigitalMKT = (filters: SummaryFilters = {}) => {
  const queryString = buildQueryString({
    year: 2025,
    last_n_years: 4,
    ...filters,
  })
  const url = `/summary/patients/digital-mkt/chart/?${queryString}`

  const snapshot = useSWRImmutable<
    CustomResponse<WithCommentary<PatientsData>>
  >(url, createFetcher)

  return {
    ...snapshot,
    data: snapshot.data?.data || DEFAULT_CHART_DATA_WITH_COMMENTARY,
  }
}
