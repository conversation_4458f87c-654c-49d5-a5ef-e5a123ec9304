from django.core.management.base import BaseCommand
from accounting.models import JournalEntryTransaction
import pandas as pd
from django_pandas.io import read_frame
from utils.beefy_connection import call_agent
from datetime import datetime
import time
import json
from analytics.models import AnalyticsSummary
from textwrap import dedent

def determine_which_columns_to_group_by(account_type, data_head, previous_total, current_total, delta, percentage_change, period_comparison):
    filtered_data = data_head.drop(columns=["journal_entry_id__transaction_date", "chart_of_account__level", "reference_1", "reference_2", "journal_entry_id__memo", "memo", "journal_entry_id__transaction_amount", "journal_entry_id__reporting_amount", "transaction_debit_amount", "transaction_credit_amount", "reporting_debit_amount", "reporting_credit_amount", "account_code", "offset_account_code"])

    system_prompt = """
    You are a top tier financial analyst specializing in analyzing Chart of Accounts (COA) data. Your expertise lies in identifying key drivers of financial changes and providing actionable insights for finance leadership.
    
    Your task is to analyze financial data and recommend the most impactful columns for grouping that will help explain variations in financial performance across different time periods.

    Context for the columns:
    journal_entry_id__transaction_amount: The total amount of the parent journal entry in its original transaction currency  
    journal_entry_id__transaction_currency: The currency code (e.g. "USD", "SGD") of the parent journal entry's transaction amount  
    journal_entry_id__reporting_currency: The currency code used for reporting/financial statements on the parent journal entry  
    journal_entry_id__reporting_amount: The total amount of the parent journal entry converted into the reporting currency  
    transaction_currency: The currency code for this individual transaction line  
    transaction_debit_amount: The debit portion of this line, expressed in the transaction currency  
    transaction_credit_amount: The credit portion of this line, expressed in the transaction currency  
    reporting_currency: The currency code for reporting amounts on this transaction line  
    reporting_debit_amount: The debit portion of this line, converted into the reporting currency  
    reporting_credit_amount: The credit portion of this line, converted into the reporting currency  
    account_code: The code identifying the primary account impacted by this transaction line  
    offset_account_code: The code of the counter-account (the other side of the double-entry) for this transaction line   
    chart_of_account__account_code: The code from the linked Chart of Accounts record  
    chart_of_account__account_name: The human-readable name of the linked Chart of Accounts record  
    chart_of_account__clinic_code: A custom code for the clinic or department associated with this account  
    chart_of_account__clinic_name: The name of the clinic or department associated with this account  
    chart_of_account__account_type: The broad category of the account (e.g. Asset, Liability, Equity, Revenue, Expense)  
    """
    human_prompt = f"""
    Account type: {account_type}
    Data sample: {data_head}
    Previous period total: {previous_total}
    Current period total: {current_total}
    Change: {delta}
    Percentage change: {percentage_change}%
    Period comparison: {period_comparison}

    Based on the provided financial data for {account_type} accounts and the {percentage_change}% change between periods ({period_comparison}), recommend the top 5 most critical columns to group by.
    
    These columns should help identify the key drivers behind the financial performance change and provide meaningful insights for the finance leadership team.
    
    Consider factors like:
    1. Business unit/cost center groupings
    2. Transaction characteristics
    3. Account hierarchies
    4. Reference fields that might indicate business activities
    5. Any other dimensions that could explain the {percentage_change}% change in performance
    6. You are able to group by UP TO 2 columns at once to provide more granularity. Else, you can group by 1 column by giving an array of 1 column name.
    
    Return EXACTLY 5 column names from the available data that would provide the most valuable groupings for analysis. 
    DO NOT RETURN ANYTHING ELSE.
    Column names MUST ONLY BE from {filtered_data.columns}

    OUTPUT FORMAT:
    [
        {{"column_name": ["<column_name_x>", "<column_name_y (optional)>"], "reasoning": "<reasoning_1>"}},
        {{"column_name": ["<column_name_x>", "<column_name_y (optional)>"], "reasoning": "<reasoning_2>"}},
        {{"column_name": ["<column_name_x>", "<column_name_y (optional)>"], "reasoning": "<reasoning_3>"}},
        {{"column_name": ["<column_name_x>", "<column_name_y (optional)>"], "reasoning": "<reasoning_4>"}},
        {{"column_name": ["<column_name_x>", "<column_name_y (optional)>"], "reasoning": "<reasoning_5>"}}
    ]
    """
    attempt = 0
    while attempt < 5:
        try:
            response = call_agent(system_prompt, human_prompt, output_model=str)
            response = json.loads(response)
            return response
        except Exception as e:
            print(e)
            attempt += 1
            time.sleep(1)
    
    raise Exception("Failed to get groupby columns")


def get_comparison_dates(period_comparison: str, start_date: datetime):
    """
    Calculate previous and current period dates based on the comparison type (YoY, QoQ, MoM)
    
    Args:
        period_comparison: String indicating type of comparison ('YoY', 'QoQ', or 'MoM')
        start_date: datetime object for the start of current period
        
    Returns:
        tuple: (previous_start, previous_end, current_start, current_end)
    """
    if period_comparison == "YoY":
        # Year over Year comparison - Jan 1st to Dec 31st
        current_start = start_date.replace(month=1, day=1)
        current_end = start_date.replace(month=12, day=31)
        previous_start = current_start.replace(year=current_start.year - 1)
        previous_end = current_end.replace(year=current_end.year - 1)
        
    elif period_comparison == "QoQ":
        # Quarter over Quarter comparison
        # Subtract 3 months from start date
        previous_start = start_date.replace(month=((start_date.month-3-1)%12)+1)
        if start_date.month <= 3:
            previous_start = previous_start.replace(year=start_date.year-1)
            
        # Previous end is one month after previous start    
        previous_end = previous_start.replace(month=previous_start.month + 1)
        if previous_start.month == 12:
            previous_end = previous_start.replace(year=previous_start.year + 1, month=1)
            
        # Current period
        current_start = start_date
        end_date = start_date.replace(month=start_date.month + 1)
        if start_date.month == 12:
            end_date = start_date.replace(year=start_date.year + 1, month=1)
        current_end = end_date
            
    elif period_comparison == "MoM":
        # Month over Month comparison
        previous_start = start_date.replace(month=((start_date.month-2)%12)+1)
        if start_date.month == 1:
            previous_start = previous_start.replace(year=start_date.year-1)
            
        # Previous end is one month after previous start
        previous_end = previous_start.replace(month=previous_start.month + 1)
        if previous_start.month == 12:
            previous_end = previous_start.replace(year=previous_start.year + 1, month=1)
            
        # Current period
        current_start = start_date
        end_date = start_date.replace(month=start_date.month + 1)
        if start_date.month == 12:
            end_date = start_date.replace(year=start_date.year + 1, month=1)
        current_end = end_date
            
    else:
        raise ValueError(f"Invalid period comparison type: {period_comparison}")
        
    return (previous_start, previous_end, current_start, current_end)


def generate_commentary(grouped_data, account_type, previous_start, previous_end, start_date, end_date, delta, percentage_change, reason):
    system_prompt = """
    You are a top tier financial analyst specializing in analyzing financial data and providing clear, concise explanations of financial performance changes.
    Your task is to analyze grouped financial data and explain the key drivers behind changes in performance.
    
    Guidelines for your analysis:
    1. Focus on the most significant factors driving change
    2. Use specific numbers and percentages to support your observations
    3. Highlight both positive and negative impacts
    4. Keep explanations clear and business-focused
    5. Maintain a professional, analytical tone
    6. Use meaningful business references like clinic names, locations, or departments rather than IDs or reference numbers
    7. If for any amount that is more than 1000000, we need to show in millions with Xm where X is the number and m is the suffix. This is to allow for better readability.
    """
    
    human_prompt = f"""
    Please analyze the following financial data for {account_type} accounts for {reason}:
    
    Time Period: 
    - Previous Period: {previous_start} to {previous_end}
    - Current Period: {start_date} to {end_date}
    
    Data Analysis Results:
    # Previous Period
    {grouped_data["previous_period"]}
    # Current Period
    {grouped_data["current_period"]}
    
    Please provide:
    1. A 3-5 line summary of the overall change. You MUST explain what led to the change (Delta amount: {delta}, Percentage change: {percentage_change}%).
    2. If delta is positive:
       - Top 3 areas that positively contributed to the increase (reference by clinic name, location or department)
       - 1 significant area that showed decline despite overall growth
    3. If delta is negative:
       - Top 3 areas that negatively impacted performance (reference by clinic name, location or department)
       - 1 significant area that showed improvement despite overall decline
    4. Explain what is the main driver of the change using meaningful business references (avoid using IDs or reference numbers).
    5. You should always use the OUTPUT FORMAT PROVIDED ONLY. Make sure to include + for positive and - for negative.
    
    Format your response as a clear, business-focused analysis that a CFO would find valuable.
    - Use specific numbers and percentages from the data to support your observations.
    - Always refer to business units by their clinic names, locations, or departments rather than IDs or reference numbers.

    # OUTPUT FORMAT
    Overall the business declined/ increased by +/-XX, largely driven by
    1) +/-X in the P business
    2) +/-Y in the Q business
    3) +/-Z in the R business
    
    Partially offset by uplift driven by
    1) AA in the S business

    These trends persisted throughout {previous_start.date()} - {previous_end.date()} and has mellowed in {start_date.date()} - {end_date.date()} in accordance to seasonality in the business.
    """
    
    try:
        response = call_agent(system_prompt, human_prompt, output_model=str)
        return response
        
    except Exception as e:
        print(f"Error generating commentary: {e}")
        return "Unable to generate commentary due to an error."


def generate_executive_summary(commentaries, account_type, delta, percentage_change, period_comparison, previous_start, previous_end, start_date, end_date):
    system_prompt = """
    You are a CFO's advisor pinpointing the single most critical performance driver.  
    Deliver concise, quantified root-cause insights solely based on the OUTPUT FORMAT PROVIDED ONLY.  
    Keep language direct and factual.
    You should not use any fancy words or phrases. Just use the data provided to you.

    Rules:
    1. All numberings must follow UK English format.
    2. DO NOT GIVE ANY ANALYSIS. JUST USE THE OUTPUT FORMAT PROVIDED ONLY.
    """

    human_prompt = f"""
    Review this {account_type} analysis over {period_comparison} (Δ {delta:,.2f} / {percentage_change:,.2f}%):

    These are the statistics of the {account_type} accounts:
    {commentaries}

    Write a summary FOLLOWING THE OUTPUT FORMAT BELOW:

    # OUTPUT FORMAT:
    Overall the business declined/ increased by +/-XX, largely driven by
    1) +/-X in the P business
    2) +/-Y in the Q business
    3) +/-Z in the R business
    
    Partially offset by uplift driven by
    1) AA in the S business

    These trends persisted throughout {previous_start.date()} - {previous_end.date()} and has mellowed in {start_date.date()} - {end_date.date()} in accordance to seasonality in the business.
    """
    try:
        response = call_agent(system_prompt, human_prompt, output_model=str)
        response =  dedent(response)
    except Exception as e:
        print(f"Error generating executive summary: {e}")

    print("BEFORE FORMATTING")
    print(response)

    # Need to convert the numbers to millions or billions
    system_prompt = f"""
    You are a top tier commentary formatter. You CFO has given you a commentary and you need to format the numbers to millions presentation to make it more readable for your management team.

    YOU MUST NOT CHANGE THE FORMAT OR CONTENT OF THE COMMENTARY.
    ONLY FORMAT THE NUMBERS TO MILLIONS whenever possible.

    Examples:
    1. 1000000 -> 1m

    # OUTPUT FORMAT:
    Overall the business declined/ increased by +/-XXm, largely driven by
    1) +/-Xm in the P business
    2) +/-Ym in the Q business
    3) +/-Zm in the R business
    
    Partially offset by uplift driven by
    1) AAm in the S business

    These trends persisted throughout {previous_start.date()} - {previous_end.date()} and has mellowed in {start_date.date()} - {end_date.date()} in accordance to seasonality in the business.
    """

    user_prompt = f"""
    Commentary: {response}

    Format the numbers to millions whenever possible. DO NOT CHANGE THE FORMAT OR CONTENT OF THE COMMENTARY.
    DO NOT INPUT ANY OF YOUR OWN ANALYSIS, COMMENTS OR THOUGHTS. JUST UPDATE THE FORMATTING OF THE NUMBERS and return the commentary.
    """
    try:
        response = call_agent(system_prompt, user_prompt, output_model=str)
        return response
    except Exception as e:
        print(f"Error formatting commentary: {e}")
        return response

def generate_grouped_commentary(previous_journal_entries, current_journal_entries, account_type, previous_start, previous_end, start_date, end_date, delta, percentage_change, grouping_type):
    """
    Generate commentary for a specific grouping of journal entries data.
    
    Args:
        previous_journal_entries (pd.DataFrame): DataFrame containing previous period entries
        current_journal_entries (pd.DataFrame): DataFrame containing current period entries 
        account_type (str): The type of account being analyzed
        previous_start (datetime): Start date of previous period
        previous_end (datetime): End date of previous period
        start_date (datetime): Start date of current period
        end_date (datetime): End date of current period
        delta (float): Change between periods
        percentage_change (float): Percentage change between periods
        grouping_type (str): Type of grouping to perform ('clinic_name', 'clinic_and_account', or 'account_name')
    
    Returns:
        str: Generated commentary
    """
    
    grouping_configs = {
        'clinic_name': {
            'columns': ['chart_of_account__clinic_name'],
            'reason': 'Analyzing the performance of each clinic'
        },
        'clinic_and_account': {
            'columns': ['chart_of_account__clinic_name', 'chart_of_account__account_name'],
            'reason': 'Analyzing the performance of each clinic by the chart of account name'
        },
        'account_name': {
            'columns': ['chart_of_account__account_name'],
            'reason': 'Analyzing the performance of each chart of account name only'
        }
    }
    
    if grouping_type not in grouping_configs:
        raise ValueError(f"Invalid grouping_type: {grouping_type}")
    
    config = grouping_configs[grouping_type]
    
    # Perform grouping
    previous_grouped_data = previous_journal_entries.groupby(config['columns']).agg({
        "reporting_debit_amount": "sum",
        "reporting_credit_amount": "sum",
        "reporting_currency": "first",
    }).reset_index()
    previous_grouped_data['transaction_amount'] = previous_grouped_data['reporting_debit_amount'] - previous_grouped_data['reporting_credit_amount']
    previous_grouped_data.drop(columns=['reporting_debit_amount', 'reporting_credit_amount'], inplace=True)
    
    current_grouped_data = current_journal_entries.groupby(config['columns']).agg({
        "reporting_debit_amount": "sum",
        "reporting_credit_amount": "sum",
        "reporting_currency": "first",
    }).reset_index()
    current_grouped_data['transaction_amount'] = current_grouped_data['reporting_debit_amount'] - current_grouped_data['reporting_credit_amount']
    current_grouped_data.drop(columns=['reporting_debit_amount', 'reporting_credit_amount'], inplace=True)

    if account_type[0] in ["Revenues", 'Income', 'Incomes', 'Other Income', 'Other Incomes', 'Liabilities', 'Liability', 'Equity', 'Equities']:
        previous_grouped_data['transaction_amount'] = previous_grouped_data['transaction_amount'] * -1
        current_grouped_data['transaction_amount'] = current_grouped_data['transaction_amount'] * -1

    # Print sums for previous period
    print("\nPrevious Period Totals:")
    print(f"Total Debit Amount: {previous_grouped_data['transaction_amount'].sum():.2f}")

    # Print sums for current period  
    print("\nCurrent Period Totals:")
    print(f"Total Debit Amount: {current_grouped_data['transaction_amount'].sum():.2f}")
    
    # Generate commentary
    commentary = generate_commentary(
        grouped_data={
            "previous_period": previous_grouped_data,
            "current_period": current_grouped_data,
        },
        account_type=account_type,
        previous_start=previous_start,
        previous_end=previous_end,
        start_date=start_date,
        end_date=end_date,
        delta=delta,
        percentage_change=percentage_change,
        reason=config['reason']
    )
    
    print(f"Commentary for {grouping_type}: {commentary}")
    print(commentary)
    print("\n" + "="*100 + "\n")
    
    return commentary


class Command(BaseCommand):
    help = "Generates summary view commentary"

    def handle(self, *args, **options):
        print("Start of generating summary view commentary")

        # 1. Define the COA account types that we are running for
        account_types = [["Revenues", "Income"],
                         ["Expenses", "Expense"],
                         ['Cost of sales']]
        period_comparison = "YoY"
        start_date = "2024-01-01"
        start_date_object = datetime.strptime(start_date, "%Y-%m-%d")
        
        previous_start, previous_end, start_date, end_date = get_comparison_dates(period_comparison, start_date_object)
        print(previous_start, previous_end, start_date, end_date)

        # 2. Query the JE for the transactions
        for account_type in account_types:
            journal_entries = JournalEntryTransaction.objects.filter(
                chart_of_account__account_type__in=account_type,
                transaction_date__range=[previous_start, end_date],
            ).select_related("chart_of_account").select_related("journal_entry")

            print(len(journal_entries))

            # 3. Convert the journal entries queryset into a dataframe
            journal_entries = read_frame(journal_entries, fieldnames=[
                "journal_entry_id__transaction_date",
                "journal_entry_id__reporting_currency",
                "journal_entry_id__reporting_amount",
                "memo",
                "reporting_currency",
                "reporting_debit_amount",
                "reporting_credit_amount",
                "account_code",
                "offset_account_code",
                "reference_1",
                "reference_2",
                "chart_of_account__account_code",
                "chart_of_account__account_name",
                "chart_of_account__level",
                "chart_of_account__clinic_code",
                "chart_of_account__clinic_name",
                "chart_of_account__account_type",
            ])

            # Calculate the total and delta
            if period_comparison == "YoY":
                previous_journal_entries = journal_entries[pd.to_datetime(journal_entries["journal_entry_id__transaction_date"]).dt.year == previous_start.year]

                current_journal_entries = journal_entries[pd.to_datetime(journal_entries["journal_entry_id__transaction_date"]).dt.year == start_date.year]
                
                previous_total = previous_journal_entries["reporting_debit_amount"].sum() - previous_journal_entries["reporting_credit_amount"].sum()
                current_total = current_journal_entries["reporting_debit_amount"].sum() - current_journal_entries["reporting_credit_amount"].sum()

                if account_type[0] in ["Revenues", 'Income', 'Incomes', 'Other Income', 'Other Incomes', 'Liabilities', 'Liability', 'Equity', 'Equities']:
                    previous_total = previous_total * -1
                    current_total = current_total * -1

                delta = current_total - previous_total
                percentage_change = (delta / previous_total) * 100 if previous_total != 0 else 100
                # Convert all to 2 decimal places
                previous_total = round(previous_total, 2)
                current_total = round(current_total, 2)
                delta = round(delta, 2)
                percentage_change = round(percentage_change, 2)
                print(previous_total, current_total, delta, percentage_change)

            else:
                raise Exception("Invalid period comparison")
            
            # 6. Generate the commentary for the data
            all_commentaries = []
            
            # 1. Group by clinic_name
            commentary = generate_grouped_commentary(
                previous_journal_entries,
                current_journal_entries,
                account_type,
                previous_start,
                previous_end,
                start_date,
                end_date,
                delta,
                percentage_change,
                'clinic_name'
            )
            all_commentaries.append(commentary)
            
            # 2. Group by clinic_name and account_name
            commentary = generate_grouped_commentary(
                previous_journal_entries,
                current_journal_entries,
                account_type,
                previous_start,
                previous_end,
                start_date,
                end_date,
                delta,
                percentage_change,
                'clinic_and_account'
            )
            all_commentaries.append(commentary)
            
            # 3. Group by just account_name
            commentary = generate_grouped_commentary(
                previous_journal_entries,
                current_journal_entries,
                account_type,
                previous_start,
                previous_end,
                start_date,
                end_date,
                delta,
                percentage_change,
                'account_name'
            )
            all_commentaries.append(commentary)

            # Generate final executive summary
            executive_summary = generate_executive_summary(
                commentaries=all_commentaries,
                account_type=account_type,
                delta=delta,
                percentage_change=percentage_change,
                period_comparison=period_comparison,
                previous_start=previous_start,
                previous_end=previous_end,
                start_date=start_date,
                end_date=end_date
            )
            print("\nEXECUTIVE SUMMARY for", account_type)
            print("="*50)
            print(executive_summary)
            print("="*50)

            # Check if the analytics summary already exists
            analytics_summary, created = AnalyticsSummary.objects.get_or_create(
                category_type=account_type[0],
                period_type=period_comparison,
                period_start_date=start_date,
                period_end_date=end_date
            )
            
            analytics_summary.commentary = executive_summary
            analytics_summary.save()
            
            print(f"Analytics summary for {account_type} created/updated")
