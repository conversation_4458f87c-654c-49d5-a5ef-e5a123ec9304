# Generated by Django 5.2.1 on 2025-07-31 03:38

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounting', '0019_patientmkt_alter_appointments_appointment_date_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='FinancialTransactionSnapshot',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('batch_number', models.CharField(db_index=True, max_length=100)),
                ('financial_statement_type', models.CharField(choices=[('revenue', 'Revenue'), ('gross_profit', 'Gross Profit'), ('ebitda', 'EBITDA'), ('net_profit', 'Net Profit'), ('bank_debt', 'Bank Debt'), ('total_debt', 'Total Debt'), ('cash_balance', 'Cash Balance'), ('operating_cash_flow', 'Operating Cash Flow'), ('free_cash_flow', 'Free Cash Flow')], db_index=True, max_length=20)),
                ('year', models.IntegerField(db_index=True)),
                ('month', models.IntegerField(db_index=True)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15)),
                ('chart_of_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.chartofaccount')),
                ('clinic', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.clinic')),
                ('journal_entry_transaction', models.ManyToManyField(to='accounting.journalentrytransaction')),
            ],
        ),
        migrations.CreateModel(
            name='Commentary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('chart_name', models.CharField(db_index=True, max_length=255)),
                ('commentary', models.TextField()),
                ('financial_transaction_snapshot', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='summary.financialtransactionsnapshot')),
            ],
            options={
                'verbose_name_plural': 'commentaries',
            },
        ),
        migrations.AddIndex(
            model_name='financialtransactionsnapshot',
            index=models.Index(fields=['batch_number'], name='summary_fin_batch_n_6a3eeb_idx'),
        ),
        migrations.AddIndex(
            model_name='financialtransactionsnapshot',
            index=models.Index(fields=['financial_statement_type'], name='summary_fin_financi_710858_idx'),
        ),
        migrations.AddIndex(
            model_name='financialtransactionsnapshot',
            index=models.Index(fields=['year'], name='summary_fin_year_aaf427_idx'),
        ),
        migrations.AddIndex(
            model_name='financialtransactionsnapshot',
            index=models.Index(fields=['month'], name='summary_fin_month_1eda17_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='financialtransactionsnapshot',
            unique_together={('batch_number', 'clinic', 'financial_statement_type', 'year', 'month')},
        ),
    ]
