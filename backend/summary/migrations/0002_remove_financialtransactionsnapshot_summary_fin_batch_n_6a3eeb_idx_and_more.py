# Generated by Django 5.2.1 on 2025-07-31 04:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('summary', '0001_initial'),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='financialtransactionsnapshot',
            name='summary_fin_batch_n_6a3eeb_idx',
        ),
        migrations.RemoveIndex(
            model_name='financialtransactionsnapshot',
            name='summary_fin_financi_710858_idx',
        ),
        migrations.RemoveIndex(
            model_name='financialtransactionsnapshot',
            name='summary_fin_year_aaf427_idx',
        ),
        migrations.RemoveIndex(
            model_name='financialtransactionsnapshot',
            name='summary_fin_month_1eda17_idx',
        ),
        migrations.AlterUniqueTogether(
            name='financialtransactionsnapshot',
            unique_together=set(),
        ),
        migrations.AlterField(
            model_name='financialtransactionsnapshot',
            name='financial_statement_type',
            field=models.CharField(choices=[('revenue', 'Revenue'), ('cost_of_sales', 'Cost of Sales'), ('operating_expenses', 'Operating Expenses'), ('other_income', 'Other Income'), ('other_expenses', 'Other Expenses'), ('gross_profit', 'Gross Profit'), ('ebitda', 'EBITDA'), ('net_profit', 'Net Profit'), ('bank_debt', 'Bank Debt'), ('total_debt', 'Total Debt'), ('cash_balance', 'Cash Balance'), ('operating_cash_flow', 'Operating Cash Flow'), ('free_cash_flow', 'Free Cash Flow')], db_index=True, max_length=20),
        ),
    ]
