from django.db import models
from utils.models import BaseModel
from accounting.models import Clinic, ChartofAccount, JournalEntryTransaction


class FinancialTransactionSnapshot(BaseModel):
    STATEMENT_TYPES = [
        ('revenue', 'Revenue'),
        ('cost_of_sales', 'Cost of Sales'),
        ('operating_expenses', 'Operating Expenses'),
        ('other_income', 'Other Income'),
        ('other_expenses', 'Other Expenses'),
        ('gross_profit', 'Gross Profit'),
        ('ebitda', 'EBITDA'),
        ('net_profit', 'Net Profit'),
        ('bank_debt', 'Bank Debt'),
        ('total_debt', 'Total Debt'),
        ('cash_balance', 'Cash Balance'),
        ('operating_cash_flow', 'Operating Cash Flow'),
        ('free_cash_flow', 'Free Cash Flow'),
    ]

    batch_number = models.CharField(max_length=100, db_index=True)
    clinic = models.ForeignKey(Clinic, on_delete=models.CASCADE, null=True)
    financial_statement_type = models.CharField(max_length=20, choices=STATEMENT_TYPES, db_index=True)
    chart_of_account = models.ForeignKey(ChartofAccount, on_delete=models.CASCADE, null=True)
    journal_entry_transaction = models.ManyToManyField(JournalEntryTransaction)
    year = models.IntegerField(db_index=True)
    month = models.IntegerField(db_index=True)
    amount = models.DecimalField(max_digits=15, decimal_places=2)

    def __str__(self):
        return f"{self.clinic.name} - {self.get_financial_statement_type_display()} - {self.year}/{self.month}"


# Create your models here.
class Commentary(BaseModel):
    chart_name = models.CharField(max_length=255, db_index=True)
    commentary = models.TextField()

    snapshot_period = models.CharField(max_length=255, null=True)

    class Meta:
        verbose_name_plural = "commentaries"

    def __str__(self):
        return f"{self.chart_name} - {self.created_at.strftime('%Y-%m-%d')}"


