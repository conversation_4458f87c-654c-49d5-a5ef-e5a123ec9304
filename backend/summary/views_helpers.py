from django.db.models import Sum, F, Value, DecimalField
from django.db.models.functions import Coalesce, ExtractYear

from accounting.models import (
    ChartofAccount,
    JournalEntryTransaction,
    Clinic,
    Employee,
    Patients,
    Appointments,
    PatientMKT,
)


def get_revenue_chart_entries(start_year, end_year, currency, breakdown=False):
    # Get chart of accounts for Revenues/Income
    revenue_account_qs = ChartofAccount.objects.filter(
        account_type__in=["Revenues", "Income"], entity__currency=currency
    ).select_related("entity")

    print("Length of revenue_account_qs", revenue_account_qs.count())
    print(start_year, end_year)

    # Query all journal entries in range and group by year
    if breakdown:
        entries = (
            JournalEntryTransaction.objects.filter(
                chart_of_account__in=revenue_account_qs,
                transaction_date__year__gte=start_year - 1,
                transaction_date__year__lte=end_year,
            ).select_related("chart_of_account")
            .exclude(memo__icontains="FOR CLOSING")
            .annotate(year=ExtractYear("transaction_date"))
            .values("year", "chart_of_account__clinic_code")
            .annotate(
                total_credit=Coalesce(
                    Sum("reporting_credit_amount"),
                    Value(0),
                    output_field=DecimalField(),
                ),
                total_debit=Coalesce(
                    Sum("reporting_debit_amount"),
                    Value(0),
                    output_field=DecimalField(),
                ),
            )
        )
    
    else:
        entries = (
            JournalEntryTransaction.objects.filter(
                chart_of_account__in=revenue_account_qs,
                transaction_date__year__gte=start_year - 1,
                transaction_date__year__lte=end_year,
            ).select_related("chart_of_account")
            .exclude(memo__icontains="FOR CLOSING")
            .annotate(year=ExtractYear("transaction_date"))
            .values("year")
            .annotate(
                total_credit=Coalesce(
                    Sum("reporting_credit_amount"),
                    Value(0),
                    output_field=DecimalField(),
                ),
                total_debit=Coalesce(
                    Sum("reporting_debit_amount"),
                    Value(0),
                    output_field=DecimalField(),
                ),
            )
        )
    print("Length of entries", len(entries))
    return entries