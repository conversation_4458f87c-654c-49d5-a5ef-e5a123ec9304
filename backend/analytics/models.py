from django.db import models

# Create your models here.
from utils.models import BaseModel, DEFAULT_CHAR_FIELD_MAX_LENGTH


class AnalyticsSummary(BaseModel):
    category_type = models.CharField(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH,
        help_text="Type of analytics category, Expense, Revenue, Cost of sales"
    )
    commentary = models.TextField(
        blank=True,
        null=True,
        help_text="Commentary or analysis notes"
    )
    period_type = models.CharField(
        max_length=DEFAULT_CHAR_FIELD_MAX_LENGTH,
        db_index=True,
        null=True,
        help_text="Period type e.g. YoY, MoM, YTD"
    )
    period_start_date = models.DateField(
        db_index=True,
        null=True,
        help_text="Period start date"
    )
    period_end_date = models.DateField(
        db_index=True,
        null=True,
        help_text="Period end date"
    )
    
class AnalysisRequest(BaseModel):
    """Primary table for analysis requests"""
    type_of_analysis = models.Char<PERSON><PERSON>(max_length=50)
    segment_type = models.Char<PERSON>ield(max_length=50)
    start_period = models.DateField()
    end_period = models.DateField()
    title = models.CharField(max_length=255, null=True, blank=True)
    
    STATUS_CHOICES = [
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='processing', db_index=True)
    data_points_analyzed = models.IntegerField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    error_message = models.TextField(null=True, blank=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['status'], name='idx_status'),
        ]


class AnalysisContent(BaseModel):
    """Content table for analysis markdown sections"""
    analysis_request = models.ForeignKey(
        AnalysisRequest, 
        on_delete=models.CASCADE, 
        related_name='content_sections'
    )
    section_order = models.IntegerField()
    section_title = models.CharField(max_length=255, null=True, blank=True)
    markdown_content = models.TextField(null=True, blank=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['analysis_request', 'section_order'], name='idx_analysis_section'),
        ]
        ordering = ['section_order']
    
    def __str__(self):
        return f"{self.analysis_request} - Section {self.section_order}"


class AnalysisChart(BaseModel):
    """Charts table for analysis visualizations"""
    analysis_request = models.ForeignKey(
        AnalysisRequest, 
        on_delete=models.CASCADE, 
        related_name='charts'
    )
    chart_order = models.IntegerField()
    chart_title = models.CharField(max_length=255, null=True, blank=True)
    
    CHART_TYPE_CHOICES = [
        ('line', 'Line Chart'),
        ('bar', 'Bar Chart'),
        ('pie', 'Pie Chart'),
        ('scatter', 'Scatter Plot'),
        ('area', 'Area Chart'),
        ('donut', 'Donut Chart'),
    ]
    chart_type = models.CharField(max_length=50, choices=CHART_TYPE_CHOICES, null=True, blank=True)
    chart_data = models.JSONField()  # Chart.js/D3.js compatible data
    chart_config = models.JSONField(null=True, blank=True)  # Chart styling/options
    section_reference = models.CharField(max_length=100, null=True, blank=True)  # Links to markdown section
    
    class Meta:
        indexes = [
            models.Index(fields=['analysis_request', 'chart_order'], name='idx_analysis_chart'),
        ]
        ordering = ['chart_order']
    
    def __str__(self):
        return f"{self.analysis_request} - Chart {self.chart_order}"
