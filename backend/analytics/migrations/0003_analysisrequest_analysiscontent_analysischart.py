# Generated by Django 5.2.1 on 2025-07-31 02:41

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('analytics', '0002_analyticssummary_period_end_date_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='AnalysisRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('type_of_analysis', models.CharField(max_length=50)),
                ('segment_type', models.Char<PERSON>ield(max_length=50)),
                ('start_period', models.DateField()),
                ('end_period', models.DateField()),
                ('title', models.CharField(blank=True, max_length=255, null=True)),
                ('status', models.Char<PERSON>ield(choices=[('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], db_index=True, default='processing', max_length=20)),
                ('data_points_analyzed', models.IntegerField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True, null=True)),
            ],
            options={
                'indexes': [models.Index(fields=['status'], name='idx_status')],
            },
        ),
        migrations.CreateModel(
            name='AnalysisContent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('section_order', models.IntegerField()),
                ('section_title', models.CharField(blank=True, max_length=255, null=True)),
                ('markdown_content', models.TextField(blank=True, null=True)),
                ('analysis_request', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='content_sections', to='analytics.analysisrequest')),
            ],
            options={
                'ordering': ['section_order'],
                'indexes': [models.Index(fields=['analysis_request', 'section_order'], name='idx_analysis_section')],
            },
        ),
        migrations.CreateModel(
            name='AnalysisChart',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('chart_order', models.IntegerField()),
                ('chart_title', models.CharField(blank=True, max_length=255, null=True)),
                ('chart_type', models.CharField(blank=True, choices=[('line', 'Line Chart'), ('bar', 'Bar Chart'), ('pie', 'Pie Chart'), ('scatter', 'Scatter Plot'), ('area', 'Area Chart'), ('donut', 'Donut Chart')], max_length=50, null=True)),
                ('chart_data', models.JSONField()),
                ('chart_config', models.JSONField(blank=True, null=True)),
                ('section_reference', models.CharField(blank=True, max_length=100, null=True)),
                ('analysis_request', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='charts', to='analytics.analysisrequest')),
            ],
            options={
                'ordering': ['chart_order'],
                'indexes': [models.Index(fields=['analysis_request', 'chart_order'], name='idx_analysis_chart')],
            },
        ),
    ]
