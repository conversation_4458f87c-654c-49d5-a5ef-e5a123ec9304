import json
import logging
import os
import traceback
import requests
from dateutil.parser import parse

logger = logging.getLogger(__name__)

BASE_URL = os.getenv("BACKEND_HOST")
BATCH_SIZE = 500
START_DATE = "2021-01-01"
END_DATE = "2025-12-31"
DATABASES = ["SEMR_Paragon", "SEMR_OUE", "SEMR_TaiThong"]


def get_cms_data(conn, access_token=None):
    try:
        for db in DATABASES:
            logger.info(f"Processing {db} from {START_DATE} to {END_DATE}")
            cursor = conn.cursor()

            # ===================== PATIENTS =====================
            patient_query = f"""
                SELECT
                    CAST(p.PatientProfileID AS NVARCHAR(50)) AS _id,
                    MIN(v.TimeIn) AS created_on
                FROM
                    {db}.PAT.PatientProfile p
                JOIN
                    {db}.CMS.Visit v ON p.PatientProfileID = v.PatientProfileFK
                WHERE
                    p.IsDeleted = 0 AND v.IsDeleted = 0 AND v.TimeIn IS NOT NULL
                    AND v.TimeIn BETWEEN '{START_DATE}' AND '{END_DATE}'
                GROUP BY p.PatientProfileID
                FOR JSON AUTO
            """
            try:
                cursor.execute(patient_query)
                rows = cursor.fetchall()
                json_string = "".join([row[0] for row in rows])
                patient_data = json.loads(json_string)

                for p in patient_data:
                    p["_id"] = p["_id"].strip()
                    p["created_on"] = (
                        parse(p["created_on"]).date().isoformat()
                        if p["created_on"]
                        else None
                    )

                # ✅ Use expected field names: _id, created_on
                patient_payload = [
                    {"_id": p["_id"], "created_on": p["created_on"]}
                    for p in patient_data
                    if p["_id"] and p["created_on"]
                ]

                if patient_payload:
                    url = f"{BASE_URL}/accounting/patients/bulk-import/?db={db}"
                    post_in_batches(url, patient_payload, access_token, "Patients", db)
            except Exception:
                logger.error(f"Failed patient sync for {db}: {traceback.format_exc()}")

            # ===================== APPOINTMENTS =====================
            appointment_query = f"""
                SELECT
                    CAST(VisitID AS NVARCHAR(50)) AS _id,
                    TimeIn AS appointment_date,
                    CAST(PatientProfileFK AS NVARCHAR(50)) AS patient_id
                FROM
                    {db}.CMS.Visit
                WHERE
                    IsDeleted = 0 AND TimeIn IS NOT NULL
                    AND TimeIn BETWEEN '{START_DATE}' AND '{END_DATE}'
                    AND PatientProfileFK IS NOT NULL
                FOR JSON AUTO
            """
            try:
                cursor.execute(appointment_query)
                rows = cursor.fetchall()
                json_string = "".join([row[0] for row in rows])
                appointment_data = json.loads(json_string)

                for a in appointment_data:
                    a["_id"] = a["_id"].strip()
                    a["patient_id"] = a["patient_id"].strip()
                    a["appointment_date"] = (
                        parse(a["appointment_date"]).date().isoformat()
                        if a["appointment_date"]
                        else None
                    )

                # ✅ Use expected field names: _id, patient_id, appointment_date
                appointment_payload = [
                    {
                        "_id": a["_id"],
                        "patient_id": a["patient_id"],
                        "appointment_date": a["appointment_date"],
                    }
                    for a in appointment_data
                    if a["_id"] and a["patient_id"] and a["appointment_date"]
                ]

                if appointment_payload:
                    url = f"{BASE_URL}/accounting/appointments/bulk-import/?db={db}"
                    post_in_batches(
                        url, appointment_payload, access_token, "Appointments", db
                    )
            except Exception:
                logger.error(
                    f"Failed appointment sync for {db}: {traceback.format_exc()}"
                )

    except Exception:
        logger.error(f"Unexpected error: {traceback.format_exc()}")


def post_in_batches(url, data_list, access_token, label, db_name):
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
    }
    total = len(data_list)
    num_batches = (total + BATCH_SIZE - 1) // BATCH_SIZE

    for i in range(num_batches):
        start = i * BATCH_SIZE
        end = min(start + BATCH_SIZE, total)
        batch = data_list[start:end]
        try:
            response = requests.post(url, headers=headers, json=batch)
            if response.status_code in (200, 201):
                try:
                    resp_json = response.json().get("data", {})
                    created = (
                        resp_json.get("created_patients")
                        or resp_json.get("created_appointments")
                        or 0
                    )
                    updated = (
                        resp_json.get("updated_patients")
                        or resp_json.get("updated_appointments")
                        or 0
                    )
                    time_taken = resp_json.get("execution_time", "N/A")
                    logger.info(
                        f"[{label}] Batch {i+1}/{num_batches} for {db_name} successful: "
                        f"Created={created}, Updated={updated}, Time={time_taken}"
                    )
                except Exception as e:
                    logger.warning(
                        f"[{label}] Batch {i+1}/{num_batches} for {db_name} succeeded but failed to parse response: {str(e)}"
                    )
            else:
                logger.error(
                    f"[{label}] Batch {i+1}/{num_batches} for {db_name} failed: {response.status_code} - {response.text}"
                )
        except Exception as e:
            logger.error(f"[{label}] Batch {i+1} failed with exception: {str(e)}")
